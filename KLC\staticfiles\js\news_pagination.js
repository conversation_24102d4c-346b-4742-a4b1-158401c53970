// Professional News Pagination
document.addEventListener("DOMContentLoaded", function () {
  // Function to adjust card heights based on content
  function adjustCardHeights() {
    // Get all small news cards in the current active carousel item
    const activeItem = document.querySelector('.carousel-item.active');
    if (activeItem) {
      const smallCards = activeItem.querySelectorAll('.small-news-card');
      const mediumCards = activeItem.querySelectorAll('.medium-news-card');

      // Reset heights to auto first
      smallCards.forEach(card => {
        card.style.height = 'auto';
      });

      mediumCards.forEach(card => {
        card.style.height = 'auto';
      });

      // Force browser reflow
      void activeItem.offsetHeight;

      // Set minimum heights based on content for small cards
      smallCards.forEach(card => {
        const content = card.querySelector('.small-news-content');
        if (content) {
          // Set minimum height based on content plus some padding
          const minHeight = Math.max(content.scrollHeight + 20, 120);
          card.style.minHeight = minHeight + 'px';
        }
      });

      // Set minimum heights based on content for medium cards
      mediumCards.forEach(card => {
        const content = card.querySelector('.medium-news-content');
        if (content) {
          // Set minimum height based on content plus some padding
          const minHeight = Math.max(content.scrollHeight + 20, 120);
          card.style.minHeight = minHeight + 'px';
        }
      });

      // Adjust featured news card if present
      const featuredCard = activeItem.querySelector('.featured-news-card');
      if (featuredCard) {
        const overlay = featuredCard.querySelector('.featured-news-overlay');
        if (overlay) {
          // Adjust overlay height based on content
          const contentHeight = overlay.scrollHeight;
          // Set minimum height but allow it to grow with content
          overlay.style.minHeight = Math.max(contentHeight, 150) + 'px';
        }
      }
    }
  }

  // Get the carousel element
  const newsCarousel = document.getElementById("newsCarousel");

  if (newsCarousel) {
    // Initialize Bootstrap carousel with no auto-rotation
    const carousel = new bootstrap.Carousel(newsCarousel, {
      interval: false, // No auto-rotation
      wrap: true,      // Allow wrapping around
      keyboard: true   // Enable keyboard navigation
    });

    // Get all carousel items (pages)
    const carouselItems = newsCarousel.querySelectorAll('.carousel-item');
    const totalPages = carouselItems.length;

    // Update the page indicator and pagination UI
    function updatePageIndicator() {
      const activeItem = newsCarousel.querySelector('.carousel-item.active');
      const currentPage = Array.from(carouselItems).indexOf(activeItem) + 1;

      // Update the current page display
      const currentPageEl = document.getElementById('currentNewsPage');
      if (currentPageEl) {
        currentPageEl.textContent = currentPage;
      }

      // Update the total pages display
      const totalPagesEl = document.getElementById('totalNewsPages');
      if (totalPagesEl) {
        totalPagesEl.textContent = totalPages;
      }

      // Update active state of pagination buttons
      document.querySelectorAll('.news-page-link').forEach(link => {
        const pageNum = parseInt(link.getAttribute('data-page'));
        const pageItem = link.closest('.page-item');

        if (pageNum === currentPage) {
          pageItem.classList.add('active');
          link.setAttribute('aria-current', 'page');
        } else {
          pageItem.classList.remove('active');
          link.removeAttribute('aria-current');
        }
      });

      // Update URL with the current page without reloading
      const url = new URL(window.location);
      url.searchParams.set('page', currentPage);
      window.history.pushState({}, '', url);

      // Update the previous/next buttons' disabled state
      updateNavigationState(currentPage);
    }

    // Update the disabled state of navigation buttons
    function updateNavigationState(currentPage) {
      // Previous button handling
      const prevPageBtn = document.getElementById('prevPageBtn');
      const prevPageItem = prevPageBtn ? prevPageBtn.closest('.page-item') : null;

      if (prevPageItem) {
        if (currentPage <= 1) {
          prevPageItem.classList.add('disabled');
          prevPageBtn.setAttribute('aria-disabled', 'true');
          prevPageBtn.setAttribute('tabindex', '-1');
        } else {
          prevPageItem.classList.remove('disabled');
          prevPageBtn.removeAttribute('aria-disabled');
          prevPageBtn.removeAttribute('tabindex');
        }
      }

      // Next button handling
      const nextPageBtn = document.getElementById('nextPageBtn');
      const nextPageItem = nextPageBtn ? nextPageBtn.closest('.page-item') : null;

      if (nextPageItem) {
        if (currentPage >= totalPages) {
          nextPageItem.classList.add('disabled');
          nextPageBtn.setAttribute('aria-disabled', 'true');
          nextPageBtn.setAttribute('tabindex', '-1');
        } else {
          nextPageItem.classList.remove('disabled');
          nextPageBtn.removeAttribute('aria-disabled');
          nextPageBtn.removeAttribute('tabindex');
        }
      }

      // Carousel control buttons
      const prevButtons = document.querySelectorAll('.carousel-control-prev');
      const nextButtons = document.querySelectorAll('.carousel-control-next');

      // Handle previous carousel buttons
      prevButtons.forEach(btn => {
        if (currentPage <= 1) {
          btn.classList.add('disabled');
          btn.setAttribute('aria-disabled', 'true');
          btn.setAttribute('tabindex', '-1');
        } else {
          btn.classList.remove('disabled');
          btn.removeAttribute('aria-disabled');
          btn.removeAttribute('tabindex');
        }
      });

      // Handle next carousel buttons
      nextButtons.forEach(btn => {
        if (currentPage >= totalPages) {
          btn.classList.add('disabled');
          btn.setAttribute('aria-disabled', 'true');
          btn.setAttribute('tabindex', '-1');
        } else {
          btn.classList.remove('disabled');
          btn.removeAttribute('aria-disabled');
          btn.removeAttribute('tabindex');
        }
      });
    }

    // Handle page navigation
    function navigateToPage(pageNumber) {
      // Ensure page number is within valid range
      pageNumber = Math.max(1, Math.min(pageNumber, totalPages));

      // Go to the specified slide (0-based index)
      carousel.to(pageNumber - 1);

      // Update the page indicator
      updatePageIndicator();

      // No automatic scrolling - user must scroll manually
    }

    // Add click event listeners to numbered pagination links
    document.querySelectorAll('.news-page-link[data-page]').forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        const targetPage = parseInt(this.getAttribute('data-page'));
        navigateToPage(targetPage);
      });
    });

    // Add click event listener to previous page button
    const prevPageBtn = document.getElementById('prevPageBtn');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', function(e) {
        e.preventDefault();
        const activeItem = newsCarousel.querySelector('.carousel-item.active');
        const currentPage = Array.from(carouselItems).indexOf(activeItem) + 1;
        navigateToPage(currentPage - 1);
      });
    }

    // Add click event listener to next page button
    const nextPageBtn = document.getElementById('nextPageBtn');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', function(e) {
        e.preventDefault();
        const activeItem = newsCarousel.querySelector('.carousel-item.active');
        const currentPage = Array.from(carouselItems).indexOf(activeItem) + 1;
        navigateToPage(currentPage + 1);
      });
    }

    // Add click event listeners to carousel control buttons
    document.querySelectorAll('.carousel-control-prev').forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        const activeItem = newsCarousel.querySelector('.carousel-item.active');
        const currentPage = Array.from(carouselItems).indexOf(activeItem) + 1;

        // Only navigate if not on first page
        if (currentPage > 1) {
          navigateToPage(currentPage - 1);
        }
      });
    });

    document.querySelectorAll('.carousel-control-next').forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        const activeItem = newsCarousel.querySelector('.carousel-item.active');
        const currentPage = Array.from(carouselItems).indexOf(activeItem) + 1;

        // Only navigate if not on last page
        if (currentPage < totalPages) {
          navigateToPage(currentPage + 1);
        }
      });
    });

    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
      // Only handle arrow keys when focus is not in an input field
      if (document.activeElement.tagName !== 'INPUT' &&
          document.activeElement.tagName !== 'TEXTAREA' &&
          document.activeElement.tagName !== 'SELECT') {

        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
          if (e.key === 'ArrowLeft') {
            const activeItem = newsCarousel.querySelector('.carousel-item.active');
            const currentPage = Array.from(carouselItems).indexOf(activeItem) + 1;
            navigateToPage(currentPage + 1); // In RTL, left arrow moves forward
          } else if (e.key === 'ArrowRight') {
            const activeItem = newsCarousel.querySelector('.carousel-item.active');
            const currentPage = Array.from(carouselItems).indexOf(activeItem) + 1;
            navigateToPage(currentPage - 1); // In RTL, right arrow moves backward
          }
        }
      }
    });

    // Update indicators when carousel slides
    newsCarousel.addEventListener('slid.bs.carousel', function() {
      updatePageIndicator();
      adjustCardHeights(); // Adjust card heights after slide
    });

    // Initialize page indicator
    updatePageIndicator();

    // Initial adjustment of card heights
    adjustCardHeights();

    // Check if there's a page parameter in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const pageParam = urlParams.get('page');

    // If there's a page parameter, navigate to that page but don't scroll
    // This prevents automatic scrolling when the page initially loads
    if (pageParam) {
      navigateToPage(parseInt(pageParam), false);
    }

    // Handle autoplay toggle
    const autoplayToggle = document.getElementById('autoplayToggle');
    if (autoplayToggle) {
      // Set initial state
      let isPlaying = true;
      autoplayToggle.classList.add('playing');

      autoplayToggle.addEventListener('click', function() {
        handleUserActivity(); // Track user activity

        if (isPlaying) {
          // Pause the carousel
          carousel.pause();
          isPlaying = false;
          userActive = true; // Force user active state

          // Clear any existing timer to prevent auto-resume
          if (activityTimer) {
            clearTimeout(activityTimer);
            activityTimer = null;
          }

          autoplayToggle.classList.remove('playing');
          autoplayToggle.classList.add('paused');
          autoplayToggle.innerHTML = '<i class="fas fa-play"></i>';
          autoplayToggle.title = 'تشغيل العرض التلقائي';
        } else {
          // Resume the carousel
          carousel.cycle();
          isPlaying = true;
          userActive = false; // Reset user active state

          autoplayToggle.classList.remove('paused');
          autoplayToggle.classList.add('playing');
          autoplayToggle.innerHTML = '<i class="fas fa-pause"></i>';
          autoplayToggle.title = 'إيقاف التشغيل التلقائي';
        }
      });

      // Update the handleUserActivity function to respect manual pause
      const originalHandleUserActivity = handleUserActivity;
      handleUserActivity = function() {
        // Only handle activity if autoplay is enabled
        if (isPlaying) {
          originalHandleUserActivity();
        }
      };
    }

    // Add touch swipe support for mobile devices
    let touchStartX = 0;
    let touchEndX = 0;

    newsCarousel.addEventListener('touchstart', function(e) {
      touchStartX = e.changedTouches[0].screenX;
      handleUserActivity();
    }, false);

    newsCarousel.addEventListener('touchend', function(e) {
      touchEndX = e.changedTouches[0].screenX;
      handleSwipe();
    }, false);

    function handleSwipe() {
      // For RTL layout, swipe directions are reversed
      if (touchEndX < touchStartX) {
        // Swipe left (next in RTL)
        const activeItem = newsCarousel.querySelector('.carousel-item.active');
        const currentPage = Array.from(carouselItems).indexOf(activeItem) + 1;
        navigateToPage(currentPage + 1);
      } else if (touchEndX > touchStartX) {
        // Swipe right (previous in RTL)
        const activeItem = newsCarousel.querySelector('.carousel-item.active');
        const currentPage = Array.from(carouselItems).indexOf(activeItem) + 1;
        navigateToPage(currentPage - 1);
      }
    }

    // Function to handle user activity
    function handleUserActivity() {
      userActive = true;
      carousel.pause(); // Pause auto-movement when user is active

      // Clear any existing timer
      if (activityTimer) {
        clearTimeout(activityTimer);
      }

      // Set a new timer to resume auto-movement after inactivity
      activityTimer = setTimeout(() => {
        userActive = false;
        carousel.cycle(); // Resume auto-movement
      }, inactivityDelay);
    }

    // Add event listeners for user activity in the news section
    const newsSection = document.getElementById('news_achievements');
    if (newsSection) {
      // Mouse movement in news section
      newsSection.addEventListener('mousemove', handleUserActivity);

      // Touch events for mobile
      newsSection.addEventListener('touchstart', handleUserActivity);

      // Clicks on any element in news section
      newsSection.addEventListener('click', handleUserActivity);

      // Scroll events in news section
      newsSection.addEventListener('scroll', handleUserActivity);
    }

    // Add activity detection to all interactive elements
    document.querySelectorAll('.news-page-link, .carousel-control-prev, .carousel-control-next').forEach(element => {
      element.addEventListener('click', handleUserActivity);
    });

    // Add window resize event listener to adjust card heights
    let resizeTimer;
    window.addEventListener('resize', function() {
      // Use debounce to avoid excessive function calls during resize
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(function() {
        adjustCardHeights();
      }, 250);
    });
  }
});
