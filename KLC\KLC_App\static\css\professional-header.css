/* Professional Header Styles */

/* Main Navigation Bar */
#mainNav {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

#mainNav .container {
  position: relative;
}

#mainNav .logo {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
}

#mainNav .logo img {
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

#mainNav .logo:hover img {
  transform: scale(1.05);
}

#mainNav .logo::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.7), transparent);
  opacity: 0;
  transition: all 0.3s ease;
}

#mainNav .logo:hover::after {
  opacity: 1;
}

/* Navigation Links */
.navbar-nav {
  margin-right: 1rem;
}

.nav-item {
  position: relative;
  margin: 0 0.25rem;
}

.nav-link {
  color: rgba(255, 255, 255, 0.85) !important;
  font-weight: 500;
  padding: 1rem 0.75rem !important;
  position: relative;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  letter-spacing: 0.5px;
}

.nav-link:hover,
.nav-link:focus {
  color: #ffffff !important;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, #ffffff, transparent);
  transition: width 0.3s ease;
}

.nav-link:hover::before {
  width: 100%;
  right: 0;
}

/* Active Navigation Link */
.nav-item.active .nav-link {
  color: #ffffff !important;
  font-weight: 600;
}

.nav-item.active .nav-link::before {
  width: 100%;
  background: linear-gradient(90deg, transparent, #ffffff, transparent);
}

/* Navbar Toggler */
.navbar-toggler {
  border: none;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.25);
}

.navbar-toggler:hover {
  background: rgba(255, 255, 255, 0.2);
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* Main Header Section */
header {
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  padding-top: 8rem;
  padding-bottom: 2rem;
  overflow: hidden;
}

/* Dynamic Background Animation */
header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(30, 60, 114, 0.9),
    rgba(42, 82, 152, 0.8),
    rgba(60, 100, 170, 0.85),
    rgba(42, 82, 152, 0.8)
  );
  background-size: 400% 400%;
  animation: gradientAnimation 15s ease infinite;
  z-index: 0;
}

@keyframes gradientAnimation {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Animated particles effect */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.particle {
  position: absolute;
  display: block;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 15s infinite ease-in-out;
}

.header-content {
  position: relative;
  z-index: 1;
  background: rgba(22, 28, 45, 0.8);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2.5rem;
  max-width: 90%;
  margin: 0 auto 3rem auto;
  transition: all 0.3s ease;
}

.header-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.header-title {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  display: inline-block;
}

.header-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.7), transparent);
}

/* Rotating Text Styles */
.header-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 1.5rem;
  font-weight: 400;
  line-height: 1.6;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.static-text {
  display: inline-block;
  margin-left: 0.5rem;
}

.rotating-text-wrapper {
  display: inline-block;
  position: relative;
  text-align: right;
  height: 1.6em;
  overflow: hidden;
}

.rotating-text {
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
  color: #ffffff;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.rotating-text.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Particle animation */
@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-1000%) rotate(720deg);
    opacity: 0;
  }
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  #mainNav {
    padding: 0.5rem 0;
  }

  .nav-link {
    padding: 0.75rem 0.5rem !important;
  }

  .header-content {
    padding: 2rem;
  }

  .header-title {
    font-size: 1.8rem;
  }

  .header-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  #mainNav .logo img {
    width: 120px;
    height: 80px;
  }

  .header-content {
    padding: 1.5rem;
  }

  .header-title {
    font-size: 1.5rem;
  }
}

/* Dark Mode Support */
[data-theme="dark"] #mainNav {
  background: linear-gradient(135deg, #0f2027 0%, #203a43 50%, #2c5364 100%) !important;
}

[data-theme="dark"] .header-content {
  background: rgba(15, 23, 42, 0.8);
}
