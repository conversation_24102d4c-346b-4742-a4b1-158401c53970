<!-- Transaction Service Modal -->
<style>
  /* Professional form styling */
  .icon-circle {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(58, 124, 165, 0.1);
  }

  .bg-info-light {
    background-color: rgba(33, 150, 243, 0.1) !important;
  }

  .bg-primary-light {
    background-color: rgba(58, 124, 165, 0.1) !important;
  }

  .input-group-focus {
    box-shadow: 0 0 0 0.25rem rgba(58, 124, 165, 0.25) !important;
    transition: all 0.2s ease-in-out;
  }

  .input-group {
    transition: all 0.2s ease-in-out;
  }

  .form-control:focus {
    box-shadow: none !important;
  }

  .professional-form {
    border-radius: 12px;
    overflow: hidden;
    padding: 2rem;
  }

  @media (max-width: 768px) {
    .professional-form {
      padding: 1.25rem;
    }
  }
</style>
<div
  class="modal fade"
  id="admintransactionsModal"
  tabindex="-1"
  aria-labelledby="admintransactionsModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="admintransactionsModalLabel"><i class="fas fa-file-alt me-2"></i> طلب معاملة</h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body p-0">
        <div class="professional-form m-0 border-0">
          <!-- Alert container for transaction messages -->
          <div id="transactionAlertContainer" class="mb-3" style="display: none;">
            <div id="transactionAlert" class="alert alert-warning text-center"></div>
          </div>

          <form
            class="admin-form"
            method="POST"
            action="{% url 'admin_make_transaction' %}"
            id="transactions-form"
            onsubmit="submitTransactionForm(event)"
          >
            {% csrf_token %}

            <div class="form-header text-center mb-4">
              <div class="icon-circle bg-primary-light mb-3 mx-auto">
                <i class="fas fa-file-signature text-primary fa-2x"></i>
              </div>
              <h3 class="fw-bold text-primary">طلب معاملة جديدة</h3>
              <p class="text-muted">يرجى إدخال بيانات المعاملة بدقة</p>
            </div>

            <div class="alert alert-warning mb-4 border-0 bg-warning bg-opacity-10">
              <div class="d-flex">
                <div class="me-3">
                  <i class="fas fa-exclamation-triangle text-warning fa-lg"></i>
                </div>
                <div>
                  تنويه: عند إختيارك لنوع الطلب، يرجى قراءة التعليمات في الأسفل قبل إرسال الطلب.
                </div>
              </div>
            </div>

            <!-- رقم الهوية -->
            <div class="form-group mb-4">
              <label for="transactionsIdNumber" class="form-label fw-bold mb-2">
                <i class="fas fa-id-card me-1 text-primary"></i>
                رقم الهوية
                <span class="text-danger">*</span>
              </label>
              <div class="input-group input-group-lg shadow-sm">
                <span class="input-group-text bg-white border-end-0">
                  <i class="fas fa-id-card text-primary"></i>
                </span>
                <input
                  type="text"
                  class="form-control border-start-0"
                  id="transactionsIdNumber"
                  name="transactionsIdNumber"
                  placeholder="أدخل رقم الهوية"
                  required
                  title="رقم الهوية"
                />
              </div>
            </div>

            <!-- الإسم الرباعي -->
            <div class="form-group mb-4">
              <label for="transactionsFullName" class="form-label fw-bold mb-2">
                <i class="fas fa-user me-1 text-primary"></i>
                الإسم الرباعي (حسب الهوية)
                <span class="text-danger">*</span>
              </label>
              <div class="input-group input-group-lg shadow-sm">
                <span class="input-group-text bg-white border-end-0">
                  <i class="fas fa-user text-primary"></i>
                </span>
                <input
                  type="text"
                  class="form-control border-start-0"
                  id="transactionsFullName"
                  name="transactionsFullName"
                  placeholder="أدخل الاسم الرباعي"
                  required
                  pattern="^[\u0621-\u064A\u0660-\u0669a-zA-Z\s]+$"
                  title="الإسم الرباعي يجب أن يتكون من حروف فقط"
                  oninput="this.value = this.value.replace(/[^a-zA-Z\u0621-\u064A\s]/g, '')"
                />
              </div>
            </div>

            <!-- رقم الهاتف -->
            <div class="form-group mb-4">
              <label for="transactionsPhoneNumber" class="form-label fw-bold mb-2">
                <i class="fas fa-phone-alt me-1 text-primary"></i>
                رقم الهاتف
                <span class="text-danger">*</span>
              </label>
              <div class="input-group input-group-lg shadow-sm">
                <span class="input-group-text bg-white border-end-0">
                  <i class="fas fa-phone-alt text-primary"></i>
                </span>
                <input
                  type="tel"
                  class="form-control border-start-0 telefone-input"
                  id="transactionsPhoneNumber"
                  name="transactionsPhoneNumber"
                  placeholder="مثال:0592345678 أو مع رقم مفتاح الدولة"
                  required
                  minlength="10"
                  maxlength="15"
                  oninput="this.value = this.value.replace(/[^0-9+]/g, '')"
                  pattern="\d{10, 15}"
                  title="رقم الهاتف يجب أن يتكون من أرقام فقط"
                />
              </div>
            </div>

            <!-- نوع المعاملة -->
            <div class="form-group mb-4">
              <label for="transactionType" class="form-label fw-bold mb-2">
                <i class="fas fa-tasks me-1 text-primary"></i>
                نوع المعاملة
                <span class="text-danger">*</span>
              </label>
              <div class="input-group input-group-lg shadow-sm">
                <span class="input-group-text bg-white border-end-0">
                  <i class="fas fa-tasks text-primary"></i>
                </span>
                <select
                  class="form-select border-start-0"
                  id="transactionType"
                  name="transactionType"
                  required
                >
                  <option value="" disabled selected hidden>
                    إختر نوع المعاملة
                  </option>
                  <option value="Route Marking">طلب تعليم طريق</option>
                  <option value="Site Plan Guide">طلب دليل مخطط موقع</option>
                  <option value="Building Permit">طلب ترخيص مبنى</option>
                  <option value="Water Line NOC">
                    طلب عدم ممانعة لتقديم خدمة خط مياه
                  </option>
                  <option value="Electricity Line NOC">
                    طلب عدم ممانعة لمد خط كهرباء
                  </option>
                  <option value="Proof of Residence">طلب إثبات سكن</option>
                  <option value="Employment Proof">طلب إثبات عمل</option>
                  <option
                    value="Request for Sale Transaction (for holders of Palestinian ID) – Plot Description and Clearance Certificate"
                  >
                    طلب معاملات صفقة بيع (لحملة الهوية الفلسطينية) وصف قطعة، براءة
                    ذمة
                  </option>
                  <option
                    value="Request for Sale Transaction (for holders of Jerusalem ID) – Plot Description, Clearance Certificate, and Classification"
                  >
                    طلب معاملات صفقة بيع (لحملة هوية القدس) وصف قطعة، براءة ذمة
                    وتصنيف
                  </option>
                  <option value="Clearance for Kushan">
                    طلب معاملة براءة ذمة لاستخراج الكوشان
                  </option>
                  <option value="other">طلب معاملة أخرى او استفسار</option>
                </select>
              </div>
            </div>

            <!-- ملاحظات إضافية -->
            <div class="form-group mb-4">
              <label for="transactionAdditionalNotes" class="form-label fw-bold mb-2">
                <i class="fas fa-comment-alt me-1 text-primary"></i>
                ملاحظات إضافية
              </label>
              <div class="input-group input-group-lg shadow-sm">
                <span class="input-group-text bg-white border-end-0">
                  <i class="fas fa-comment-alt text-primary"></i>
                </span>
                <textarea
                  class="form-control border-start-0"
                  id="transactionAdditionalNotes"
                  name="transactionAdditionalNotes"
                  rows="3"
                  placeholder="اكتب ملاحظاتك هنا... (إختياري)"
                ></textarea>
              </div>
            </div>

            <!-- تعليمات المعاملة -->
            <div
              id="transactionNotes"
              class="alert alert-warning mb-4 border-0 bg-warning bg-opacity-10 p-3 rounded"
            ></div>

            <!--Get the council contact info text-->
            {% include 'KLC_App/council_contact_info_for_services.html' %}

            <div class="form-group d-flex justify-content-between mt-5">
              <button type="button" class="btn btn-secondary btn-lg px-5" data-bs-dismiss="modal">
                <i class="fas fa-times me-2"></i> إلغاء
              </button>
              <button type="submit" class="btn btn-primary btn-lg px-5">
                <i class="fas fa-paper-plane me-2"></i> إرسال الطلب
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- JS contains a function for transaction form to display notes for each transaction-->
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Initialize the transaction form notes display
    displayTransactionFormNotes();

    // Add focus effects for professional look
    const formInputs = document.querySelectorAll('.form-control, .form-select');
    formInputs.forEach(input => {
      // Add focus effect
      input.addEventListener('focus', function() {
        this.closest('.input-group').classList.add('input-group-focus');
      });

      // Remove focus effect
      input.addEventListener('blur', function() {
        this.closest('.input-group').classList.remove('input-group-focus');
      });
    });
  });

  // Function to handle form submission
  function submitTransactionForm(event) {
    event.preventDefault();

    const form = document.getElementById('transactions-form');
    const formData = new FormData(form);
    const alertContainer = document.getElementById('transactionAlertContainer');
    const alertElement = document.getElementById('transactionAlert');

    // Hide any previous alerts
    alertContainer.style.display = 'none';

    fetch(form.action, {
      method: 'POST',
      body: formData,
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.status && data.status !== 'success') {
        // Show the alert with the error message
        alertElement.textContent = data.status;
        alertContainer.style.display = 'block';

        // Scroll to the top of the modal to show the alert
        const modalBody = document.querySelector('.modal-body');
        if (modalBody) {
          modalBody.scrollTop = 0;
        }
      } else {
        // Success - redirect to admin page
        window.location.href = '/admin';
      }
    })
    .catch(error => {
      console.error('Error:', error);
      // Fallback to normal form submission if fetch fails
      form.submit();
    });
  }

  function displayTransactionFormNotes() {
    // Get the transaction type select element and notes paragraph
    const transactionTypeSelect = document.getElementById("transactionType");
    const transactionNotes = document.getElementById("transactionNotes");
    /// Map contains the notes for each transaction type
    /////////////// Maybe the following will be changed as KLC owners needs **** Sholud be discussed with them
    const transactionNotesMap = {
      "Route Marking":
        "الرجاء تحديد المسافة المطلوبة بالمتر (الطول والعرض) وذلك في خانة الملاحظات -\n" +
        "عند قدومك للمجلس لتقديم طلب تعليم طريق، يرجى إحضار الوثائق التالية:\n" +
        "- براءة الذمة المالية من المجلس\n" +
        "- صورة حديثة عن هوية مقدم الطلب أو صاحب الأرض أو الوكيل القانوني\n" +
        "- صورة عن كوشان قطعة الأرض\n" +
        "(الرسوم المطلوبة: 200 شيكل)",

      "Site Plan Guide":
        "عند قدومك للمجلس لتقديم طلب دليل مخطط موقع، يرجى إحضار الوثائق التالية: - صورة عن كوشان قطعة الأرض - صورة عن هوية المالك سارية المفعول (الرسوم المطلوبة: 20 دينار)",

      "Building Permit":
        "عند قدومك للمجلس لتقديم طلب ترخيص مبنى، يرجى إحضار الوثائق التالية: - دليل مخطط موقع معتمد - صورة عن هوية المالك سارية المفعول - صورة عن كوشان قطعة الأرض (الرسوم تحدد حسب تصنيف الأرض ومساحة المبنى وعدد الطوابق)",

      "Water Line NOC":
        "عند قدومك للمجلس لتقديم طلب عدم ممانعة لخط مياه، يرجى إحضار الوثائق التالية: - براءة الذمة المالية من المجلس - صورة عن هوية المشترك سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

      "Electricity Line NOC":
        "عند قدومك للمجلس لتقديم طلب عدم ممانعة لخط كهرباء، يرجى إحضار الوثائق التالية: - رخصة البناء - براءة الذمة المالية من المجلس - صورة عن هوية المشترك سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

      "Proof of Residence":
        "عند قدومك للمجلس لتقديم طلب إثبات سكن، يرجى إحضار الوثائق التالية: - براءة الذمة المالية من المجلس - صورة عن الهوية الشخصية سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

      "Employment Proof":
        "عند قدومك للمجلس لتقديم طلب إثبات عمل، يرجى إحضار الوثائق التالية: - براءة الذمة المالية من المجلس - صورة عن الهوية الشخصية سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

      "Request for Sale Transaction (for holders of Palestinian ID) – Plot Description and Clearance Certificate":
        "عند قدومك للمجلس لتقديم طلب صفقة بيع (لحملة الهوية الفلسطينية)، يرجى إحضار الوثائق التالية: - براءة الذمة المالية لقطعة الأرض ومالكها - صورة عن هوية المالك سارية المفعول - كوشان قطعة الأرض (الرسوم المطلوبة: 50 شيكل للبراءة + 100 شيكل لوصف القطعة)",

      "Request for Sale Transaction (for holders of Jerusalem ID) – Plot Description, Clearance Certificate, and Classification":
        "عند قدومك للمجلس لتقديم طلب صفقة بيع (لحملة هوية القدس)، يرجى إحضار الوثائق التالية: - براءة الذمة المالية لقطعة الأرض ومالكها - صورة عن هوية المالك سارية المفعول - كوشان قطعة الأرض (الرسوم المطلوبة: 50 شيكل للبراءة + 100 شيكل لوصف القطعة + 100 شيكل للتصنيف)",

      "Clearance for Kushan":
        "عند قدومك للمجلس لتقديم طلب براءة ذمة لاستخراج الكوشان، يرجى إحضار الوثائق التالية: - براءة الذمة المالية لقطعة الأرض ومالكها - صورة عن هوية المالك سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

      other:
        "عند تقديم طلب معاملة أخرى أو إستفسار يجب عليك كتابة الطلب أو الإستفسار في قسم الملاحظات الإضافية.",
    };

    // Default note
    const defaultNote =
      "*يرجى إختيار نوع المعاملة من القائمة أعلاه لتظهر لك التعليمات الخاصة بها هنا.";

    // Set initial note
    transactionNotes.textContent = defaultNote;

    // Add event listener for changes
    transactionTypeSelect.addEventListener("change", function () {
      const selectedType = this.value;
      if (selectedType && transactionNotesMap[selectedType]) {
        transactionNotes.textContent = transactionNotesMap[selectedType];
      } else {
        transactionNotes.textContent = defaultNote;
      }
    });
  }
</script>
