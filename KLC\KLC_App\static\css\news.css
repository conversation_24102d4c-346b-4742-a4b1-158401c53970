/* News Section Styles */
.news-section {
    position: relative;
    overflow: hidden;
}

/* Override the general section styles for the news section */
.news-section p.lead::after {
    display: none; /* Remove the separator line */
}

.news-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.01) 25%, transparent 25%, transparent 75%, rgba(0,0,0,0.01) 75%),
                linear-gradient(45deg, rgba(0,0,0,0.01) 25%, transparent 25%, transparent 75%, rgba(0,0,0,0.01) 75%);
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
    opacity: 0.3;
    z-index: 0;
}

/* Section title styling - with specific news section overrides */
.news-section h2, .news-section .lead {
    position: relative;
    z-index: 1;
}

/* Card Styles */
.news-card {
    border: none;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    background-color: #fff;
    position: relative;
}

.news-card-hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-img-container {
    position: relative;
    overflow: hidden;
    border-bottom: 3px solid #dc3545;
}

.card-img-container img {
    transition: transform 0.5s ease;
    height: 180px;
    object-fit: cover;
    width: 100%;
}

.news-card:hover .card-img-container img {
    transform: scale(1.05);
}

/* Enhanced card styling */
.news-card .card-body {
    padding: 1.25rem;
    position: relative;
    padding-bottom: 4rem; /* Add extra padding at the bottom for the buttons */
    min-height: 250px; /* Set a minimum height for the card body */
}

.news-card .card-title {
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #2c3e50;
    font-size: 1.1rem;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.news-card .card-text {
    color: #5d6d7e;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    height: 4.5rem;
}

/* Button container and styling */
.news-buttons-container {
    display: flex;
    justify-content: space-between;
    position: absolute;
    bottom: 1.25rem;
    left: 1.25rem;
    right: 1.25rem;
    gap: 10px;
}

.news-read-btn, .news-video-btn {
    border-radius: 30px;
    font-size: 0.85rem;
    padding: 0.4rem 0.8rem;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.news-read-btn {
    color: #3498db;
    border-color: #3498db;
    position: absolute;
    right: 0;
}

.news-read-btn:hover {
    background-color: #3498db;
    color: white;
    transform: translateY(-3px);
}

.news-video-btn {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    position: absolute;
    left: 0;
}

.news-video-btn:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-3px);
}

.news-category {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(44, 62, 80, 0.85);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 2;
}

.news-date {
    color: #7f8c8d;
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.card-body {
    padding: 1.25rem;
}

.card-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    line-height: 1.4;
}

.card-text {
    color: #5d6d7e;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-btn {
    background-color: #3498db;
    border-color: #3498db;
    color: white;
    border-radius: 30px;
    padding: 0.4rem 1rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.news-btn:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    transform: translateX(-5px);
}

/* Carousel Styles */
.carousel {
    position: relative;
    z-index: 1;
}

.carousel-inner {
    border-radius: 12px;
    overflow: hidden;
}

.carousel-indicators {
    position: relative;
    margin-top: 1rem;
    margin-bottom: 1rem;
    justify-content: center;
    bottom: auto;
    z-index: 5;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #bdc3c7;
    opacity: 0.5;
    margin: 0 5px;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.carousel-indicators button.active {
    background-color: #dc3545;
    opacity: 1;
    transform: scale(1.2);
    box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3);
}

.carousel-controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
    z-index: 10;
}

.carousel-control-prev,
.carousel-control-next {
    position: relative;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
    background-color: white;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.carousel-control-icon {
    color: #2c3e50;
    font-size: 1rem;
}

/* View All Button */
.view-all-btn {
    border-radius: 30px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-color: #3498db;
    color: #3498db;
    margin: 0 auto;
    display: inline-block;
}

.view-all-btn:hover {
    background-color: #3498db;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
}

/* Modal Styles */
.modal-content {
    border-radius: 12px;
    overflow: hidden;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
}

.modal-title {
    color: #2c3e50;
    font-weight: 600;
}

.modal-body {
    padding: 1.5rem;
}

.modal-date {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.modal-description {
    color: #5d6d7e;
    font-size: 1rem;
    line-height: 1.6;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
}

/* News Detail Modal */
#newsDetailModal .modal-content {
    background-color: #fff;
    border-radius: 15px;
    overflow: hidden;
    border: none;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

#newsDetailModal .modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1.25rem 1.5rem;
    background-color: #f8f9fa;
}

#newsDetailModal .modal-title {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.5rem;
}

#newsDetailModal .modal-body {
    padding: 1.5rem;
}

#newsDetailModal .modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
}

.news-modal-image img {
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    width: 100%;
    max-height: 400px;
    object-fit: cover;
}

.news-modal-meta {
    background-color: #f8f9fa;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    color: #5d6d7e;
    font-size: 0.9rem;
}

.news-modal-description {
    line-height: 1.8;
    color: #2c3e50;
    font-size: 1.1rem;
    text-align: justify;
}

#showVideoBtn {
    background-color: #3498db;
    border-color: #3498db;
    transition: all 0.3s ease;
}

#showVideoBtn:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    transform: translateY(-2px);
}

/* Dark Theme Support */
/* Dark theme overrides are now handled by the general section styles */
[data-theme="dark"] .news-section::before {
    opacity: 0.05;
    background: linear-gradient(45deg, rgba(255,255,255,0.02) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.02) 75%),
                linear-gradient(45deg, rgba(255,255,255,0.02) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.02) 75%);
}

[data-theme="dark"] .news-card {
    background-color: #2c3e50;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .news-category {
    background-color: rgba(52, 152, 219, 0.85);
}

[data-theme="dark"] .news-date {
    color: #bdc3c7;
}

[data-theme="dark"] .card-title {
    color: #ecf0f1;
}

[data-theme="dark"] .card-text {
    color: #bdc3c7;
}

[data-theme="dark"] .carousel-control-prev,
[data-theme="dark"] .carousel-control-next {
    background-color: rgba(44, 62, 80, 0.9);
}

[data-theme="dark"] .carousel-control-icon {
    color: #ecf0f1;
}

[data-theme="dark"] .view-all-btn {
    border-color: #3498db;
    color: #3498db;
}

[data-theme="dark"] .view-all-btn:hover {
    background-color: #3498db;
    color: #ecf0f1;
}

[data-theme="dark"] .modal-content {
    background-color: #2c3e50;
}

[data-theme="dark"] .modal-header,
[data-theme="dark"] .modal-footer {
    background-color: #1a1a1a;
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modal-title {
    color: #ecf0f1;
}

[data-theme="dark"] .modal-date {
    color: #bdc3c7;
}

[data-theme="dark"] .modal-description {
    color: #ecf0f1;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-img-container img {
        height: 180px;
    }

    .carousel-controls {
        padding: 0 5px;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 576px) {
    .section-header h2 {
        font-size: 1.5rem;
    }

    .section-subtitle {
        font-size: 0.9rem;
    }

    .card-img-container img {
        height: 160px;
    }

    .card-title {
        font-size: 1rem;
    }

    .card-text {
        font-size: 0.85rem;
    }
}
