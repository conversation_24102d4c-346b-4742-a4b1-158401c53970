{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}الشكاوي والاقتراحات - لوحة التحكم{% endblock %}

{% block body_class %}admin-suggestions{% endblock %}

{% block extra_css %}
<style>
    /* Message cell styling */
    .message-cell {
        max-width: 300px;
        width: 300px;
    }

    .message-container {
        position: relative;
    }

    .message-content {
        max-height: 70px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        line-height: 1.5;
        margin-bottom: 8px;
        font-size: 0.95rem;
        color: #333;
    }

    [data-theme="dark"] .message-content {
        color: #e2e8f0;
    }

    /* Message actions styling */
    .message-actions {
        display: flex;
        gap: 8px;
        margin-top: 8px;
    }

    .btn-action {
        padding: 0.3rem 0.6rem;
        border-radius: 20px;
        font-size: 0.85rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 5px;
        border: none;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .view-message {
        background-color: #e3f2fd;
        color: #0d6efd;
    }

    .view-message:hover {
        background-color: #0d6efd;
        color: white;
    }

    .copy-message {
        background-color: #e8f5e9;
        color: #2e7d32;
    }

    .copy-message:hover {
        background-color: #2e7d32;
        color: white;
    }

    [data-theme="dark"] .view-message {
        background-color: rgba(13, 110, 253, 0.2);
        color: #90caf9;
    }

    [data-theme="dark"] .view-message:hover {
        background-color: #0d6efd;
        color: white;
    }

    [data-theme="dark"] .copy-message {
        background-color: rgba(46, 125, 50, 0.2);
        color: #a5d6a7;
    }

    [data-theme="dark"] .copy-message:hover {
        background-color: #2e7d32;
        color: white;
    }

    .btn-action i {
        font-size: 0.9rem;
    }

    /* Modal styling */
    .message-modal .modal-header {
        border-bottom: 3px solid #0d6efd;
    }

    .message-modal .modal-body {
        max-height: 700px;
        overflow-y: auto;
        padding: 2rem;
        line-height: 1.6;
    }

    .modal-content-large {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    [data-theme="dark"] .modal-content-large {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    /* Sender details styling */
    .sender-details {
        margin-bottom: 1.5rem;
    }

    .sender-card {
        display: flex;
        align-items: center;
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    [data-theme="dark"] .sender-card {
        background-color: #2c3e50;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .sender-avatar {
        width: 50px;
        height: 50px;
        background-color: #e3f2fd;
        color: #0d6efd;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-left: 1rem;
    }

    [data-theme="dark"] .sender-avatar {
        background-color: rgba(13, 110, 253, 0.2);
    }

    .sender-info {
        flex: 1;
    }

    .sender-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #212529;
        margin-bottom: 0.5rem;
    }

    [data-theme="dark"] .sender-name {
        color: #f8f9fa;
    }

    .sender-meta {
        display: flex;
        gap: 1rem;
        color: #6c757d;
        font-size: 0.9rem;
    }

    [data-theme="dark"] .sender-meta {
        color: #adb5bd;
    }

    /* Message container styling */
    .message-container-modal {
        margin-bottom: 1.5rem;
    }

    .message-header-vertical {
        display: flex;
        margin-bottom: 1rem;
    }

    .message-label {
        writing-mode: vertical-rl;
        transform: rotate(180deg);
        background: linear-gradient(to bottom, #0d6efd, #0a58ca);
        color: white;
        padding: 1.5rem 0.75rem;
        border-radius: 10px;
        font-weight: 600;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        margin-left: 1.5rem;
        box-shadow: 0 4px 12px rgba(13, 110, 253, 0.25);
        position: relative;
        overflow: hidden;
    }

    .message-label::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
    }

    .message-label i {
        font-size: 1.5rem;
        margin-bottom: 0.75rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .message-label span {
        font-size: 1.1rem;
        letter-spacing: 1px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .message-textbox-container {
        flex: 1;
    }

    .message-textbox {
        width: 100%;
        min-height: 300px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        font-size: 1.05rem;
        line-height: 1.7;
        color: #212529;
        resize: none;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
        border-right: 4px solid #0d6efd;
        font-family: inherit;
        direction: rtl;
        transition: all 0.3s ease;
    }

    .message-textbox:focus {
        outline: none;
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    [data-theme="dark"] .message-textbox {
        background-color: #343a40;
        border-color: #495057;
        color: #e9ecef;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    [data-theme="dark"] .message-textbox:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    /* Message actions styling */
    .message-actions-modal {
        display: flex;
        justify-content: flex-start;
        margin-top: 1rem;
    }

    .btn-action-modal {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        background-color: #e8f5e9;
        color: #2e7d32;
        border: none;
        font-weight: 500;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn-action-modal:hover {
        background-color: #2e7d32;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    [data-theme="dark"] .btn-action-modal {
        background-color: rgba(46, 125, 50, 0.2);
        color: #a5d6a7;
    }

    [data-theme="dark"] .btn-action-modal:hover {
        background-color: #2e7d32;
        color: white;
    }

    /* Success state for copy button */
    .success-copy {
        background-color: #2e7d32 !important;
        color: white !important;
    }

    /* Table styling */
    .message-column {
        width: 40% !important;
    }

    #suggestionsTable td {
        vertical-align: middle;
    }

    /* Ensure table maintains structure */
    #suggestionsTable {
        table-layout: fixed;
        width: 100%;
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
        .message-cell {
            max-width: 200px;
            width: 200px;
        }

        .message-content {
            max-height: 60px;
            -webkit-line-clamp: 2;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="section-header">
    <h2><i class="fas fa-comment-alt me-2"></i> الشكاوي والاقتراحات</h2>
</div>

<!-- Suggestions Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">
            <i class="fas fa-comments me-2"></i> قائمة الشكاوي والاقتراحات
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table align-middle" id="suggestionsTable">
                <thead>
                    <tr>
                        <th scope="col" width="5%">#</th>
                        <th scope="col" width="15%">رقم الهوية</th>
                        <th scope="col" width="15%">الاسم</th>
                        <th scope="col" width="40%" class="message-column">الرسالة</th>
                        <th scope="col" width="15%">تاريخ الإرسال</th>
                        <th scope="col" width="10%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for suggestion in suggestions %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ suggestion.id_number }}</td>
                        <td>{{ suggestion.full_name }}</td>
                        <td class="message-cell">
                            <div class="message-container">
                                <div class="message-content">{{ suggestion.message }}</div>
                                {% if suggestion.message|length > 50 %}
                                <div class="message-actions">
                                    <button class="btn btn-sm btn-action view-message" data-bs-toggle="modal" data-bs-target="#messageModal" data-message="{{ suggestion.message }}" data-name="{{ suggestion.full_name }}" data-id="{{ suggestion.id_number }}" data-date="{{ suggestion.created_at }}">
                                        <i class="fas fa-eye"></i>
                                        <span class="action-text">عرض الرسالة</span>
                                    </button>
                                    <button class="btn btn-sm btn-action copy-message" data-message="{{ suggestion.message }}">
                                        <i class="fas fa-copy"></i>
                                        <span class="action-text">نسخ</span>
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ suggestion.created_at }}</td>
                        <td>
                            <form method="POST" action="{% url 'delete_suggestion_admin' suggestion.id %}" style="display: inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-danger btn-sm" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا الاقتراح؟')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center">لا يوجد شكاوي أو اقتراحات</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Message Modal -->
<div class="modal fade message-modal" id="messageModal" tabindex="-1" aria-labelledby="messageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content modal-content-large">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="messageModalLabel">
                    <i class="fas fa-comment-alt me-2"></i>
                    تفاصيل الرسالة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="sender-details">
                    <div class="sender-card">
                        <div class="sender-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="sender-info">
                            <h6 class="sender-name" id="messageSender"></h6>
                            <div class="sender-meta">
                                <span class="sender-id"><i class="fas fa-id-card me-1"></i> <span id="messageId"></span></span>
                                <span class="sender-date"><i class="fas fa-calendar-alt me-1"></i> <span id="messageDate"></span></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="message-container-modal">
                    <div class="message-header-vertical">
                        <div class="message-label">
                            <i class="fas fa-envelope-open-text"></i>
                            <span>نص الرسالة</span>
                        </div>
                        <div class="message-textbox-container">
                            <textarea class="message-textbox" id="messageText" readonly></textarea>
                        </div>
                    </div>
                </div>

                <div class="message-actions-modal">
                    <button type="button" class="btn btn-action-modal copy-full-message">
                        <i class="fas fa-copy me-1"></i>
                        نسخ الرسالة
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function () {
        $('#suggestionsTable').DataTable({
            language: {
                url: "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            responsive: true,
            autoWidth: false,
            columnDefs: [
                { width: "5%", targets: 0 },
                { width: "15%", targets: 1 },
                { width: "15%", targets: 2 },
                { width: "40%", targets: 3 },
                { width: "15%", targets: 4 },
                { width: "10%", targets: 5 }
            ],
            dom: '<"dt-controls"<"row"<"col-md-6"l><"col-md-6"f>>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row mt-3"<"col-sm-5"i><"col-sm-7"p>>',
            pageLength: 10,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
            buttons: [
                {
                    extend: 'copyHtml5',
                    text: '<i class="fas fa-copy"></i> نسخ',
                    className: 'btn btn-secondary btn-sm'
                },
                {
                    extend: 'excelHtml5',
                    text: '<i class="fas fa-file-excel"></i> تصدير Excel',
                    className: 'btn btn-success btn-sm'
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> طباعة',
                    className: 'btn btn-primary btn-sm'
                }
            ],
            initComplete: function () {
                $('#suggestionsTable_wrapper .dt-buttons').addClass('mb-3');


                // Add placeholder to search input
                $('.dataTables_filter input').attr('placeholder', 'البحث...');
            }
        });

        // Handle message modal
        $('#messageModal').on('show.bs.modal', function (event) {
            const button = $(event.relatedTarget);
            const message = button.data('message');
            const name = button.data('name');
            const id = button.data('id');
            const date = button.data('date');

            $('#messageSender').text(name);
            $('#messageId').text(id);
            $('#messageDate').text(date);
            $('#messageText').val(message);
        });

        // Handle copy message button in table
        $('.copy-message').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const message = $(this).data('message');
            copyToClipboard(message);

            // Show success feedback
            const originalText = $(this).html();
            $(this).html('<i class="fas fa-check"></i><span class="action-text">تم النسخ</span>');

            setTimeout(() => {
                $(this).html(originalText);
            }, 2000);
        });

        // Handle copy full message button in modal
        $('.copy-full-message').on('click', function() {
            const message = $('#messageText').val();
            copyToClipboard(message);

            // Show success feedback
            const originalText = $(this).html();
            $(this).html('<i class="fas fa-check me-1"></i> تم النسخ');
            $(this).addClass('success-copy');

            setTimeout(() => {
                $(this).html(originalText);
                $(this).removeClass('success-copy');
            }, 2000);
        });

        // Auto-resize textarea based on content
        $('#messageModal').on('shown.bs.modal', function() {
            // Adjust textarea height to fit content
            const textarea = document.getElementById('messageText');
            textarea.style.height = 'auto';
            textarea.style.height = (textarea.scrollHeight + 5) + 'px';
        });

        // Helper function to copy text to clipboard
        function copyToClipboard(text) {
            // Create a temporary textarea element
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.setAttribute('readonly', '');
            textarea.style.position = 'absolute';
            textarea.style.left = '-9999px';
            document.body.appendChild(textarea);

            // Select and copy the text
            textarea.select();
            document.execCommand('copy');

            // Remove the temporary element
            document.body.removeChild(textarea);
        }
    });
</script>
{% endblock %}
