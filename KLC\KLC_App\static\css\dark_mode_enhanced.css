/* Enhanced Dark Mode Styles */

/* Smooth transition between themes */
body {
    transition: background-color 0.5s ease, color 0.5s ease;
}

.theme-transition * {
    transition: background-color 0.5s ease,
                color 0.5s ease,
                border-color 0.5s ease,
                box-shadow 0.5s ease !important;
}

.card, .table, .form-control, .btn, .alert, .modal-content {
    transition: background-color 0.5s ease,
                color 0.5s ease,
                border-color 0.5s ease,
                box-shadow 0.5s ease;
}

/* Base styles for dark mode */
[data-theme="dark"] {
    /* Background colors */
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --card-bg: #1e1e1e;
    --table-bg: #1e1e1e;
    --hover-bg: #2d2d2d;

    /* Text colors - increased contrast */
    --text-primary: #ffffff;
    --text-secondary: #cccccc;

    /* Border colors */
    --border-color: #333333;
    --table-border: #333333;

    /* Input fields */
    --input-bg: #2d2d2d;
    --input-border: #444444;

    /* Shadow colors */
    --shadow-color: rgba(0, 0, 0, 0.5);
}

/* Body background */
[data-theme="dark"] body {
    background: linear-gradient(135deg, #121212 0%, #1a1a1a 100%);
    color: var(--text-primary);
}

/* Sidebar */
[data-theme="dark"] .sidebar {
    background: #0a0a0a;
    color: #ffffff;
    box-shadow: 2px 0 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .sidebar .nav-link {
    color: #b0b0b0;
}

[data-theme="dark"] .sidebar .nav-link:hover,
[data-theme="dark"] .sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
}

/* Main content area */
[data-theme="dark"] .main-content {
    background: var(--bg-primary);
}

[data-theme="dark"] .section-header {
    border-bottom-color: #333333;
}

[data-theme="dark"] .section-header h2 {
    color: #60a5fa;
}

/* Cards */
[data-theme="dark"] .card {
    background: #1e1e1e;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .card-header {
    background-color: #252525 !important;
    border-bottom-color: #333333;
}

[data-theme="dark"] .card-title,
[data-theme="dark"] .card-subtitle {
    color: #ffffff !important;
}

/* Dashboard summary cards */
[data-theme="dark"] .card.shadow-sm.h-100 {
    background: #1e1e1e !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .card.shadow-sm.h-100 .card-title,
[data-theme="dark"] .card.shadow-sm.h-100 .card-subtitle,
[data-theme="dark"] .card.shadow-sm.h-100 h3,
[data-theme="dark"] .card.shadow-sm.h-100 h6 {
    color: #ffffff !important;
}

[data-theme="dark"] .card.shadow-sm.h-100 .text-muted {
    color: #b0b0b0 !important;
}

[data-theme="dark"] .card.shadow-sm.h-100 .rounded-circle {
    background-color: #252525 !important;
}

/* Tables */
[data-theme="dark"] .table {
    color: #ffffff;
    background-color: #1e1e1e;
}

[data-theme="dark"] .table thead {
    background-color: #2563eb !important;
    color: #ffffff;
}

[data-theme="dark"] .table thead th {
    color: #ffffff !important;
    border-bottom-color: #333333 !important;
}

[data-theme="dark"] .table tbody tr:nth-child(even) {
    background-color: #252525;
}

[data-theme="dark"] .table tbody tr:nth-child(odd) {
    background-color: #1e1e1e;
}

[data-theme="dark"] .table tbody tr:hover {
    background-color: #2d2d2d !important;
    color: #ffffff;
}

[data-theme="dark"] .table td,
[data-theme="dark"] .table th {
    border-color: #333333;
}

/* Form controls */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: #2d2d2d;
    border-color: #444444;
    color: #ffffff;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: #2d2d2d;
    border-color: #60a5fa;
    color: #ffffff;
    box-shadow: 0 0 0 0.25rem rgba(96, 165, 250, 0.25);
}

[data-theme="dark"] .form-control::placeholder {
    color: #999999;
}

[data-theme="dark"] .form-label {
    color: #ffffff;
}

/* Buttons */
[data-theme="dark"] .btn-primary {
    background: linear-gradient(90deg, #2563eb 60%, #1e40af 100%);
    border: none;
}

[data-theme="dark"] .btn-secondary {
    background-color: #4b5563;
    border-color: #4b5563;
}

[data-theme="dark"] .btn-success {
    background: linear-gradient(90deg, #22c55e 60%, #15803d 100%);
    border: none;
}

[data-theme="dark"] .btn-danger {
    background: linear-gradient(90deg, #ef4444 60%, #991b1b 100%);
    border: none;
}

[data-theme="dark"] .btn-warning {
    background: linear-gradient(90deg, #f59e42 60%, #b45309 100%);
    border: none;
}

[data-theme="dark"] .btn-info {
    background: linear-gradient(90deg, #0ea5e9 60%, #0369a1 100%);
    border: none;
}

[data-theme="dark"] .btn-outline-primary {
    color: #60a5fa;
    border-color: #60a5fa;
}

[data-theme="dark"] .btn-outline-primary:hover {
    background-color: #2563eb;
    color: #ffffff;
}

/* Alerts */
[data-theme="dark"] .alert {
    background-color: #252525;
    border-color: #333333;
}

[data-theme="dark"] .alert-success {
    color: #22c55e;
    border-left: 4px solid #22c55e;
}

[data-theme="dark"] .alert-danger {
    color: #ef4444;
    border-left: 4px solid #ef4444;
}

[data-theme="dark"] .alert-warning {
    color: #f59e42;
    border-left: 4px solid #f59e42;
}

[data-theme="dark"] .alert-info {
    color: #0ea5e9;
    border-left: 4px solid #0ea5e9;
}

/* DataTables */
[data-theme="dark"] .dataTables_wrapper .dataTables_length label,
[data-theme="dark"] .dataTables_wrapper .dataTables_filter label {
    background-color: #252525;
    color: #ffffff;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_length select,
[data-theme="dark"] .dataTables_wrapper .dataTables_filter input {
    background-color: #2d2d2d;
    border-color: #444444;
    color: #ffffff;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_info,
[data-theme="dark"] .dataTables_wrapper .dataTables_paginate {
    color: #ffffff !important;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button {
    color: #ffffff !important;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
}

[data-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
    color: #ffffff !important;
}

/* Modal dialogs */
[data-theme="dark"] .modal-content {
    background-color: #1e1e1e;
    border-color: #333333;
}

[data-theme="dark"] .modal-header {
    border-bottom-color: #333333;
}

[data-theme="dark"] .modal-footer {
    border-top-color: #333333;
}

[data-theme="dark"] .modal-title {
    color: #ffffff;
}

/* Statistics page specific */
[data-theme="dark"] .stat-card,
[data-theme="dark"] .stat-card-main {
    background-color: #1e1e1e !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .stat-card-main {
    background: linear-gradient(90deg, #1e40af 0%, #2563eb 100%) !important;
}

[data-theme="dark"] .stat-card .icon-circle {
    background-color: #252525 !important;
}

[data-theme="dark"] .stat-card .text-info {
    color: #38bdf8 !important;
}

[data-theme="dark"] .stat-card .text-success {
    color: #22c55e !important;
}

[data-theme="dark"] .stat-card .stat-percentage {
    color: #60a5fa !important;
}

[data-theme="dark"] .custom-table thead th {
    background: #1e40af !important;
    border-bottom: 2px solid #2563eb !important;
}

[data-theme="dark"] .custom-table tbody tr:nth-child(even) {
    background-color: #252525 !important;
}

[data-theme="dark"] .custom-table tbody tr:nth-child(odd) {
    background-color: #1e1e1e !important;
}

[data-theme="dark"] .custom-table tbody tr:hover {
    background-color: #2d2d2d !important;
}

/* Activity date badge */
[data-theme="dark"] .activity-date {
    background-color: rgba(37, 99, 235, 0.2) !important;
    color: #60a5fa !important;
}

/* Receipt number */
[data-theme="dark"] td[style*="color: #2563eb"] {
    color: #60a5fa !important;
}

/* News section title */
.news-section-title {
    color: #000000; /* Black for light mode */
}

[data-theme="dark"] .news-section-title {
    color: #ffffff !important; /* White for dark mode */
}

/* News section subtitle */
.news-section-subtitle {
    color: #8B0000; /* Dark red for light mode */
}

[data-theme="dark"] .news-section-subtitle {
    color: #ff6b6b !important; /* Light red for dark mode */
}

/* Water Service Modal Dark Mode */
[data-theme="dark"] #waterServiceModal .modal-content {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

[data-theme="dark"] #waterServiceModal .modal-body {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

[data-theme="dark"] #waterServiceModal .service-instructions ol li {
    color: #ffffff !important;
}

[data-theme="dark"] #waterServiceModal .list-styled li {
    color: #ffffff !important;
}

[data-theme="dark"] #waterServiceModal .service-info p {
    color: #cccccc !important;
}

[data-theme="dark"] #waterServiceModal .list-group-item {
    background-color: transparent !important;
    color: #ffffff !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] #waterServiceModal .service-benefits .list-group .list-group-item {
    color: #ffffff !important;
    background-color: transparent !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] #waterServiceModal .service-benefits .list-group-flush .list-group-item {
    color: #ffffff !important;
    background-color: transparent !important;
}

[data-theme="dark"] #waterServiceModal .service-benefits ul li {
    color: #ffffff !important;
}

[data-theme="dark"] #waterServiceModal .service-benefits h5 {
    color: #60a5fa !important;
}

[data-theme="dark"] #waterServiceModal .service-header h4 {
    color: #60a5fa !important;
}

[data-theme="dark"] #waterServiceModal .service-header p {
    color: #cccccc !important;
}

[data-theme="dark"] #waterServiceModal .service-instructions h5 {
    color: #60a5fa !important;
}

[data-theme="dark"] #waterServiceModal .working-hours h4 {
    color: #60a5fa !important;
}

[data-theme="dark"] #waterServiceModal .working-hours h5 {
    color: #60a5fa !important;
}

[data-theme="dark"] #waterServiceModal .working-hours p {
    color: #ffffff !important;
}

[data-theme="dark"] #waterServiceModal .contact-info h5 {
    color: #60a5fa !important;
}

[data-theme="dark"] #waterServiceModal .contact-info p {
    color: #ffffff !important;
}

[data-theme="dark"] #waterServiceModal .hours-card {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
}

[data-theme="dark"] #waterServiceModal .contact-info {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
}

[data-theme="dark"] #waterServiceModal .modal-footer {
    background-color: #2d2d2d !important;
    color: #cccccc !important;
}

/* Extra specific selectors to override Bootstrap styles */
[data-theme="dark"] #waterServiceModal .service-benefits ul.list-group li.list-group-item {
    color: #ffffff !important;
    background-color: transparent !important;
}

[data-theme="dark"] #waterServiceModal .service-benefits ul.list-group.list-group-flush li.list-group-item {
    color: #ffffff !important;
    background-color: transparent !important;
}

[data-theme="dark"] #waterServiceModal .service-benefits .list-group-item.bg-transparent {
    color: #ffffff !important;
    background-color: transparent !important;
}

/* Force override for any text inside the benefits section */
[data-theme="dark"] #waterServiceModal .service-benefits * {
    color: #ffffff !important;
}

[data-theme="dark"] #waterServiceModal .service-benefits h5 {
    color: #60a5fa !important;
}

[data-theme="dark"] #waterServiceModal .service-benefits i.fas {
    color: #22c55e !important;
}
