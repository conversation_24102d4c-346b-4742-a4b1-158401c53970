{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}إدارة المشرفين - لوحة التحكم{% endblock %}

{% block body_class %}admin-management{% endblock %}

{% block content %}
<div class="section-header">
    <h2><i class="fas fa-user-shield me-2"></i> إدارة المشرفين</h2>
</div>

{% if access_denied %}
<!-- Access Denied Section -->
<div class="card mb-4">
    <div class="card-header bg-danger text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i> غير مصرح بالوصول
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <i class="fas fa-lock me-2"></i>
            <strong>عذراً!</strong> ليس لديك صلاحية للوصول إلى هذه الصفحة. هذه الصفحة متاحة فقط للمشرف "ahmad".
        </div>
        <a href="{% url 'admin_dashboard' %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
        </a>
    </div>
</div>
{% else %}
<!-- Password Protection Section -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-lock me-2"></i> منطقة محمية
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            هذه المنطقة محمية بكلمة مرور خاصة. يرجى إدخال كلمة المرور الإدارية للوصول إلى وظائف إدارة المشرفين.
        </div>

        <form id="adminPasswordForm" class="mb-3">
            <div class="input-group">
                <input type="password" id="adminSectionPassword" class="form-control" placeholder="أدخل كلمة المرور الإدارية">
                <button class="btn btn-outline-secondary toggle-password" type="button">
                    <i class="fas fa-eye"></i>
                </button>
                <button type="button" id="verifyPasswordBtn" class="btn btn-primary">
                    <i class="fas fa-check me-1"></i> تحقق
                </button>
            </div>
            <div class="form-text text-muted">
                <i class="fas fa-info-circle me-1"></i> كلمة المرور مطلوبة للوصول إلى وظائف إدارة المشرفين
            </div>
        </form>
    </div>
</div>
{% endif %}

{% if not access_denied %}
<!-- Admin Management Sections (Hidden by default) -->
<div id="adminManagementSections" style="display: none;">
    <div class="row">
        <!-- Change Admin Password -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-key me-2"></i> تغيير كلمة مرور المشرف
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{% url 'admin_management' %}">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="change_admin_password">
                        <input type="hidden" name="admin_password" id="hiddenAdminPassword1">

                        <div class="mb-3">
                            <label for="adminUsername" class="form-label">اسم المستخدم</label>
                            <select class="form-select" id="adminUsername" name="username" required>
                                <option value="" selected disabled>اختر المشرف</option>
                                {% for admin in admins %}
                                <option value="{{ admin }}">{{ admin }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="newPassword" class="form-label">كلمة المرور الجديدة</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="newPassword" name="new_password" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> حفظ كلمة المرور الجديدة
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Create New Admin -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i> إنشاء مشرف جديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{% url 'admin_management' %}">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="create_admin">
                        <input type="hidden" name="admin_password" id="hiddenAdminPassword2">

                        <div class="mb-3">
                            <label for="newAdminUsername" class="form-label">اسم المستخدم الجديد</label>
                            <input type="text" class="form-control" id="newAdminUsername" name="username" required>
                        </div>

                        <div class="mb-3">
                            <label for="newAdminPassword" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="newAdminPassword" name="password" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus-circle me-1"></i> إنشاء مشرف جديد
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Delete Admin -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-minus me-2"></i> حذف مشرف
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{% url 'admin_management' %}">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="delete_admin">
                        <input type="hidden" name="admin_password" id="hiddenAdminPassword4">

                        <div class="mb-3">
                            <label for="deleteAdminUsername" class="form-label">اسم المستخدم</label>
                            <select class="form-select" id="deleteAdminUsername" name="username" required>
                                <option value="" selected disabled>اختر المشرف</option>
                                {% for admin in admins %}
                                <option value="{{ admin }}">{{ admin }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تحذير:</strong> حذف المشرف سيؤدي إلى إزالة حسابه بشكل نهائي.
                        </div>

                        <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المشرف؟ هذا الإجراء لا يمكن التراجع عنه.')">
                            <i class="fas fa-trash-alt me-1"></i> حذف المشرف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmed Transactions Section -->
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-trash-alt me-2"></i> حذف المعاملات المؤكدة
            </h5>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> يمكنك حذف المعاملات المؤكدة من صفحة <a href="{% url 'transaction_statistics' %}" class="alert-link">إحصائيات الطلبات</a>. سيتم طلب كلمة مرور المدير عند محاولة حذف معاملة مؤكدة.
            </div>
            <a href="{% url 'transaction_statistics' %}" class="btn btn-outline-danger">
                <i class="fas fa-external-link-alt me-1"></i> الذهاب إلى إحصائيات الطلبات
            </a>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}



{% block extra_js %}
<script>
    $(document).ready(function() {
        // Toggle password visibility
        $('.toggle-password').on('click', function() {
            const passwordInput = $(this).closest('.input-group').find('input');
            const icon = $(this).find('i');

            if (passwordInput.attr('type') === 'password') {
                passwordInput.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                passwordInput.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });

        // Verify admin password
        $('#verifyPasswordBtn').on('click', function() {
            const password = $('#adminSectionPassword').val();

            if (password === "klc@admin") {
                // Show admin management sections
                $('#adminManagementSections').slideDown();
                $('#adminPasswordForm').slideUp();

                // Set the password in hidden fields for forms
                $('#hiddenAdminPassword1, #hiddenAdminPassword2, #hiddenAdminPassword3, #hiddenAdminPassword4').val(password);

                // Show success message
                $('.card-body').first().prepend(
                    '<div class="alert alert-success mb-3">' +
                    '<i class="fas fa-check-circle me-2"></i>' +
                    'تم التحقق بنجاح. يمكنك الآن استخدام وظائف إدارة المشرفين.' +
                    '</div>'
                );
            } else {
                // Show error message
                $('#adminSectionPassword').addClass('is-invalid');

                // Add error message if it doesn't exist
                if ($('.invalid-feedback').length === 0) {
                    $('#adminSectionPassword').after(
                        '<div class="invalid-feedback">كلمة المرور غير صحيحة</div>'
                    );
                }
            }
        });

        // Handle Enter key in password field
        $('#adminSectionPassword').on('keypress', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('#verifyPasswordBtn').click();
            }
        });

        // Add validation for delete admin form submission
        $('input[name="action"][value="delete_admin"]').closest('form').on('submit', function(e) {
            const username = $('#deleteAdminUsername').val();
            if (!username) {
                e.preventDefault();
                alert('يرجى اختيار المشرف المراد حذفه');
                return false;
            }
        });
    });
</script>
{% endblock %}
