{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <meta name="description" content="{{ news.title }} - مجلس قروي كفر عين" />
    <meta name="author" content="" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>{{ news.title }} - مجلس قروي كفر عين</title>
    <!-- Force light mode by default -->
    <script>
      // Set light mode immediately before any other scripts run
      document.documentElement.setAttribute("data-theme", "light");
      localStorage.setItem("theme", "light");
    </script>
    <!-- Bootstrap 5.3 CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Favicon-->
    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'images/logo.png' %}"
    />
    <!-- Core theme CSS (includes Bootstrap)-->
    <link href="{% static 'css/index.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Theme CSS -->
    <link href="{% static 'css/theme.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Modern News Layout CSS -->
    <link href="{% static 'css/modern-news.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- News Image Carousel CSS -->
    <link href="{% static 'css/news-image-carousel.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- News Detail CSS -->
    <link href="{% static 'css/news-detail.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      /* Professional News Detail Page Styles */
      .news-detail-container {
        padding: 3rem 0;
        background-color: #f8f9fa;
        min-height: 100vh;
      }

      .news-detail-header {
        margin-bottom: 2rem;
        position: relative;
      }

      .news-detail-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #212529;
        margin-bottom: 1rem;
        position: relative;
        padding-right: 20px;
        line-height: 1.3;
      }

      .news-detail-title::before {
        content: '';
        position: absolute;
        right: 0;
        top: 10px;
        bottom: 10px;
        width: 6px;
        background-color: #dc3545;
        border-radius: 3px;
      }

      .news-detail-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding: 1rem 1.5rem;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        border-right: 4px solid #dc3545;
      }

      .news-detail-date,
      .news-detail-category {
        display: flex;
        align-items: center;
        font-size: 1.1rem;
        color: #343a40;
        font-weight: 500;
      }

      .news-detail-date i,
      .news-detail-category i {
        color: #dc3545;
        margin-left: 10px;
        font-size: 1.2rem;
      }

      .news-image-container {
        margin-bottom: 2rem;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        background-color: #000;
      }

      .news-image {
        position: relative;
        width: 100%;
        height: 500px;
        background-color: #000;
        border-radius: 8px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .news-image img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
        display: block;
      }

      /* Loading indicator */
      .loading-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2rem;
        color: #dc3545;
        z-index: 5;
      }

      .loading-indicator i {
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }



      .news-detail-content {
        background-color: #fff;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        margin-bottom: 2rem;
      }

      .news-detail-description {
        font-size: 1.2rem;
        line-height: 1.9;
        color: #212529;
        text-align: justify;
        margin-bottom: 2rem;
      }

      .news-detail-description p {
        margin-bottom: 1.5rem;
      }

      .news-video-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
      }

      .video-container {
        position: relative;
        padding-bottom: 56.25%;
        height: 0;
        overflow: hidden;
        border-radius: 10px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(0, 0, 0, 0.1);
        background-color: #000;
      }

      .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 0;
      }

      .related-news-section {
        margin-top: 3rem;
        margin-bottom: 3rem;
      }

      .related-news-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        position: relative;
        padding-right: 15px;
      }

      .related-news-title::before {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 5px;
        height: 70%;
        background-color: #dc3545;
        border-radius: 3px;
      }

      .related-news-card {
        height: 100%;
        transition: all 0.3s ease;
        overflow: hidden;
      }

      .related-news-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      }

      .related-news-img-container {
        height: 180px;
        overflow: hidden;
        background-color: #000;
        position: relative;
      }

      .related-news-img-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .related-news-card:hover .related-news-img-container img {
        transform: scale(1.05);
      }

      .back-to-news {
        margin-top: 2rem;
        margin-bottom: 1rem;
      }

      .back-to-news .btn {
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        border-radius: 30px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(108, 117, 125, 0.3);
      }

      .back-to-news .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 15px rgba(108, 117, 125, 0.4);
      }

      /* Header styles */
      .site-header {
        background-color: #343a40;
        padding: 15px 0;
        color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .logo-container {
        display: flex;
        align-items: center;
        cursor: pointer;
        text-decoration: none;
        color: white;
        transition: opacity 0.2s ease;
      }

      .logo-container:hover {
        opacity: 0.9;
        color: white;
      }

      .logo-container img {
        max-height: 60px;
        margin-left: 15px;
      }

      .logo-text {
        font-weight: 700;
        font-size: 1.5rem;
      }

      /* Responsive header styles */
      @media (max-width: 576px) {
        .site-header {
          padding: 10px 0;
        }

        .logo-container img {
          max-height: 40px;
          margin-left: 10px;
        }

        .logo-text {
          font-size: 1.1rem;
        }

        .header-btn {
          font-size: 0.8rem;
          padding: 0.375rem 0.75rem;
        }
      }

      /* Responsive styles */
      @media (max-width: 768px) {
        .news-detail-title {
          font-size: 1.8rem;
        }

        .news-detail-meta {
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
        }

        .news-image {
          height: 350px;
        }

        .news-detail-description {
          font-size: 1.1rem;
        }
      }

      @media (max-width: 576px) {
        .news-detail-title {
          font-size: 1.5rem;
        }

        .news-image {
          height: 250px;
        }

        .news-detail-content {
          padding: 1.5rem;
        }

        .news-detail-description {
          font-size: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <header class="site-header">
      <div class="container">
        <div class="d-flex justify-content-between align-items-center">
          <a href="{% url 'index' %}" class="logo-container">
            <img src="{% static 'images/logo.png' %}" alt="شعار المجلس القروي" />
            <span class="logo-text">مجلس قروي كفر عين</span>
          </a>
          <a href="{% url 'news_list' %}" class="btn btn-outline-light header-btn">
            <i class="fas fa-newspaper me-2"></i> جميع الأخبار
          </a>
        </div>
      </div>
    </header>

    <!-- News Detail Content -->
<div class="news-detail-container">
  <div class="container">
    <div class="back-to-news">
      <a href="{% url 'index' %}#news_achievements" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i> العودة إلى الأخبار
      </a>
    </div>

    <div class="news-detail-header">
      <h1 class="news-detail-title">{{ news.title }}</h1>
      <div class="news-detail-meta">
        <div class="news-detail-date">
          <i class="fas fa-calendar-alt"></i>
          <span>{{ news.published_at }}</span>
        </div>
        <div class="news-detail-category">
          <i class="fas fa-tag"></i>
          <span>{{ news.type|default:"أخبار" }}</span>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-lg-8">
        <!-- News Image -->
        <div class="news-image-container">
          {% if news.additional_images %}
            <!-- Image carousel for news with multiple images -->
            <div class="card mb-4">
              <div class="card-img-top news-image-carousel news-detail-carousel" id="newsDetailCarousel">
                <!-- Main image slide -->
                <div class="carousel-slide active">
                  <img src="{{ news.image_src }}" alt="{{ news.title }}"
                       onerror="this.onerror=null; this.src='{% static 'images/logo.png' %}'; console.error('Failed to load image: {{ news.image_src }}');">
                </div>

                <!-- Additional image slides -->
                {% for img_src in news.additional_images %}
                  <div class="carousel-slide">
                    <img src="{{ img_src }}" alt="{{ news.title }} - صورة {{ forloop.counter|add:1 }}"
                         onerror="this.onerror=null; this.src='{% static 'images/logo.png' %}'; console.error('Failed to load image: {{ img_src }}');">
                  </div>
                {% endfor %}

                <!-- Carousel indicators -->
                <div class="carousel-indicators">
                  <!-- Will be populated by JavaScript -->
                </div>

                <!-- Carousel navigation buttons -->
                <button class="carousel-control-prev" type="button" id="prevButton">
                  <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                  <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" id="nextButton">
                  <span class="carousel-control-next-icon" aria-hidden="true"></span>
                  <span class="visually-hidden">Next</span>
                </button>
              </div>

              <div class="card-body">
                <h5 class="card-title">{{ news.title }}</h5>
                <p class="card-text"><small class="text-muted">{{ news.published_at }}</small></p>
              </div>
            </div>
          {% else %}
            <!-- Single image display -->
            <div class="card mb-4">
              <div class="card-img-top news-image" id="newsImage">
                <img src="{{ news.image_src }}" alt="{{ news.title }}"
                     onerror="this.onerror=null; this.src='{% static 'images/logo.png' %}'; console.error('Failed to load image: {{ news.image_src }}');">
              </div>
              <div class="card-body">
                <h5 class="card-title">{{ news.title }}</h5>
                <p class="card-text"><small class="text-muted">{{ news.published_at }}</small></p>
              </div>
            </div>
          {% endif %}
        </div>

        <!-- News Content -->
        <div class="news-detail-content">
          <div class="news-detail-description">
            {{ news.Description|linebreaks }}
          </div>

          <!-- Video if available -->
          {% if news.video_url %}
            <div class="news-video-container">
              <h4 class="mb-3">فيديو توضيحي</h4>
              <div class="video-container" id="videoContainer">
                <!-- Video will be loaded via JavaScript -->
              </div>
            </div>
          {% endif %}
        </div>
      </div>

      <div class="col-lg-4">
        <!-- Related News -->
        <div class="related-news-section">
          <h3 class="related-news-title">أخبار ذات صلة</h3>

          {% if related_news %}
            {% for related in related_news %}
              <div class="card related-news-card mb-4">
                <div class="related-news-img-container">
                  <img src="{{ related.image_src }}" class="card-img-top" alt="{{ related.title }}"
                       data-original-src="{{ related.image_src }}"
                       onerror="this.onerror=null; this.src='{% static 'images/logo.png' %}'; console.error('Failed to load image: {{ related.image_src }}');">
                </div>
                <div class="card-body">
                  <h5 class="card-title">{{ related.title }}</h5>
                  <p class="card-text">{{ related.Description|truncatechars:100 }}</p>
                  <a href="{% url 'news_detail' related.id %}" class="btn btn-danger">اقرأ المزيد</a>
                </div>
              </div>
            {% endfor %}
          {% else %}
            <div class="alert alert-light text-center p-4 mt-3">
              <i class="fas fa-newspaper fa-2x mb-3 text-muted"></i>
              <p class="mb-0 fs-5">لا يوجد اخبار مشابهة</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
      </div>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"></script>
    <!-- News Image Carousel JS -->
    <script src="{% static 'js/news-image-carousel.js' %}?v={{ STATIC_VERSION }}"></script>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Initialize carousel navigation buttons
        const prevButton = document.getElementById('prevButton');
        const nextButton = document.getElementById('nextButton');
        const carousel = document.getElementById('newsDetailCarousel');

        if (prevButton && nextButton && carousel) {
          // Previous button click handler
          prevButton.addEventListener('click', function() {
            const currentIndex = parseInt(carousel.dataset.currentSlide || 0);
            const slides = carousel.querySelectorAll('.carousel-slide');
            if (slides.length <= 1) return;

            // Calculate the previous slide index
            const prevIndex = (currentIndex - 1 + slides.length) % slides.length;

            // Show the previous slide
            showSlide(carousel, prevIndex);
            resetTimer(carousel);
          });

          // Next button click handler
          nextButton.addEventListener('click', function() {
            const currentIndex = parseInt(carousel.dataset.currentSlide || 0);
            const slides = carousel.querySelectorAll('.carousel-slide');
            if (slides.length <= 1) return;

            // Calculate the next slide index
            const nextIndex = (currentIndex + 1) % slides.length;

            // Show the next slide
            showSlide(carousel, nextIndex);
            resetTimer(carousel);
          });
        }

        // Single image handling
        const newsImage = document.getElementById('newsImage');
        if (newsImage) {
          const img = newsImage.querySelector('img');
          if (img) {
            // Create a loading indicator
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'loading-indicator';
            loadingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            newsImage.appendChild(loadingIndicator);

            // Set a timeout to ensure DOM has time to update
            setTimeout(() => {
              // Create a new image object to preload
              const preloadImg = new Image();

              preloadImg.onload = function() {
                // Remove loading indicator
                if (newsImage.contains(loadingIndicator)) {
                  newsImage.removeChild(loadingIndicator);
                }
                console.log('Image loaded successfully');
              };

              preloadImg.onerror = function() {
                // Image failed to load, use fallback
                img.src = "{% static 'images/logo.png' %}";
                // Remove loading indicator
                if (newsImage.contains(loadingIndicator)) {
                  newsImage.removeChild(loadingIndicator);
                }
                console.error('Failed to load image');
              };

              // Start loading the image
              preloadImg.src = img.src;
            }, 100);
          }
        }

        // Handle related news images
        const relatedNewsImages = document.querySelectorAll('.related-news-img-container img');
        relatedNewsImages.forEach(img => {
          const originalSrc = img.getAttribute('data-original-src');
          if (originalSrc) {
            // Force reload the image
            img.removeAttribute('src');

            // Create a loading indicator for the container
            const container = img.closest('.related-news-img-container');
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'loading-indicator';
            loadingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            container.appendChild(loadingIndicator);

            // Preload image
            const preloadImg = new Image();
            preloadImg.onload = function() {
              img.src = originalSrc;
              if (container.contains(loadingIndicator)) {
                container.removeChild(loadingIndicator);
              }
            };
            preloadImg.onerror = function() {
              img.src = "{% static 'images/logo.png' %}";
              if (container.contains(loadingIndicator)) {
                container.removeChild(loadingIndicator);
              }
            };
            preloadImg.src = originalSrc;
          }
        });

        // Video handling
        const videoContainer = document.getElementById('videoContainer');
        if (videoContainer) {
          const videoUrl = "{{ news.video_url|escapejs }}";
          if (videoUrl) {
            let finalVideoSrc = videoUrl;

            // Process different video sources
            if (videoUrl.includes("drive.google.com")) {
              let fileId = "";
              if (videoUrl.includes("/file/d/")) {
                fileId = videoUrl.split("/file/d/")[1].split("/")[0];
              } else if (videoUrl.includes("id=")) {
                fileId = videoUrl.split("id=")[1].split("&")[0];
              }

              if (fileId) {
                finalVideoSrc = `https://drive.google.com/file/d/${fileId}/preview`;
              }
            }
            // Handle YouTube videos
            else if (videoUrl.includes("youtube.com") || videoUrl.includes("youtu.be")) {
              if (videoUrl.includes("youtube.com/watch")) {
                const videoId = new URL(videoUrl).searchParams.get("v");
                if (videoId) {
                  finalVideoSrc = `https://www.youtube.com/embed/${videoId}?rel=0&showinfo=0&autoplay=0`;
                }
              } else if (videoUrl.includes("youtu.be")) {
                const videoId = videoUrl.split("youtu.be/")[1].split("?")[0];
                if (videoId) {
                  finalVideoSrc = `https://www.youtube.com/embed/${videoId}?rel=0&showinfo=0&autoplay=0`;
                }
              }
            }

            // Create iframe
            videoContainer.innerHTML = `
              <iframe
                src="${finalVideoSrc}"
                width="100%"
                height="100%"
                allowfullscreen
                frameborder="0"
              ></iframe>
            `;
          }
        }
      });
    </script>
  </body>
</html>
