{% extends 'KLC_App/admin/admin_base.html' %}
{% load static %}

{% block title %}تسديد الدين - لوحة التحكم{% endblock %}

{% block content %}
<div class="section-header">
    <h2><i class="fas fa-money-bill-wave me-2"></i> تسديد الدين</h2>
    <div>
        <a href="{% url 'admin_users' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للمستخدمين
        </a>
    </div>
</div>

<!-- User Information Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-user-circle me-2"></i> معلومات المستخدم
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="user-info-item">
                    <span class="info-label"><i class="fas fa-user me-2"></i> اسم المستخدم:</span>
                    <span class="info-value fw-bold" id="userName">{{ user_name }}</span>
                </div>
                <div class="user-info-item mt-3">
                    <span class="info-label"><i class="fas fa-id-card me-2"></i> رقم الهوية:</span>
                    <span class="info-value" id="userIdNumber">{{ user_id_number }}</span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="user-info-item">
                    <span class="info-label"><i class="fas fa-money-bill-wave me-2"></i> إجمالي الدين:</span>
                    <span class="info-value fw-bold text-danger" id="currentDebtAmount">{{ current_debt }}</span>
                    <span class="info-unit">شيقل</span>
                </div>
                <div class="user-info-item mt-3">
                    <span class="info-label"><i class="fas fa-calendar-alt me-2"></i> السنة:</span>
                    <span class="info-value">2024</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Form Card -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">
            <i class="fas fa-hand-holding-usd me-2"></i> تسديد الدين
        </h5>
    </div>
    <div class="card-body">
        <form id="settleDebtForm" method="POST">
            {% csrf_token %}

            <div class="mb-4">
                <label class="form-label fw-bold">مبلغ التسديد</label>
                <div class="input-group">
                    <input type="number"
                           step="0.01"
                           class="form-control"
                           name="amount"
                           required
                           max="{{ current_debt }}"
                           placeholder="أدخل مبلغ التسديد">
                    <span class="input-group-text">شيقل</span>
                </div>
                <div class="form-text text-muted">
                    أدخل المبلغ الذي تم تسديده من قبل المستخدم (الحد الأقصى: {{ current_debt }} شيقل)
                </div>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-check-circle me-1"></i>
                    تأكيد التسديد
                </button>
                <a href="{% url 'admin_users' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times-circle me-1"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* User Information Styling */
    .user-info-item {
        margin-bottom: 10px;
        padding: 8px;
        border-radius: 8px;
        background-color: rgba(0, 0, 0, 0.02);
    }

    [data-theme="dark"] .user-info-item {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .info-label {
        display: block;
        font-size: 0.9rem;
        color: #64748b;
        margin-bottom: 5px;
    }

    [data-theme="dark"] .info-label {
        color: #94a3b8;
    }

    .info-value {
        font-size: 1.1rem;
        color: #1e293b;
    }

    [data-theme="dark"] .info-value {
        color: #f1f5f9;
    }

    .info-unit {
        font-size: 0.9rem;
        color: #64748b;
        margin-right: 5px;
    }

    [data-theme="dark"] .info-unit {
        color: #94a3b8;
    }

    .text-danger {
        color: #ef4444 !important;
    }

    [data-theme="dark"] .text-danger {
        color: #f87171 !important;
    }

    /* Card Styling */
    .card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        margin-bottom: 1.5rem;
    }

    [data-theme="dark"] .card {
        background-color: #1e293b;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.12);
    }

    .card-header {
        padding: 1rem 1.5rem;
        background-color: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
    }

    [data-theme="dark"] .card-header {
        background-color: #0f172a;
        border-bottom-color: #334155;
    }

    .card-header.bg-primary {
        background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%) !important;
    }

    [data-theme="dark"] .card-header.bg-primary {
        background: linear-gradient(90deg, #1e40af 0%, #2563eb 100%) !important;
    }

    .card-title {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
    }

    [data-theme="dark"] .card-title {
        color: #f8fafc;
    }

    .card-header.bg-primary .card-title {
        color: white !important;
    }

    /* Form Styling */
    .form-control {
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }

    [data-theme="dark"] .form-control {
        background-color: #1e293b;
        border-color: #334155;
        color: #f1f5f9;
    }

    .form-control:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
    }

    .input-group-text {
        background-color: #f1f5f9;
        border: 1px solid #e2e8f0;
        color: #64748b;
    }

    [data-theme="dark"] .input-group-text {
        background-color: #334155;
        border-color: #475569;
        color: #e2e8f0;
    }

    .form-text {
        font-size: 0.875rem;
        color: #64748b;
    }

    [data-theme="dark"] .form-text {
        color: #94a3b8;
    }

    /* Button Styling */
    .btn-primary {
        background-color: #2563eb;
        border-color: #2563eb;
    }

    .btn-primary:hover {
        background-color: #1d4ed8;
        border-color: #1d4ed8;
    }

    [data-theme="dark"] .btn-primary {
        background-color: #3b82f6;
        border-color: #3b82f6;
    }

    [data-theme="dark"] .btn-primary:hover {
        background-color: #2563eb;
        border-color: #2563eb;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Set the form action
        const userId = "{{ user_id }}";
        const form = document.getElementById('settleDebtForm');
        form.action = `/pay_debt/${userId}/`;

        // Format the debt amount with commas for thousands
        const debtAmount = document.getElementById('currentDebtAmount');
        if (debtAmount) {
            const amount = parseFloat(debtAmount.textContent);
            if (!isNaN(amount)) {
                debtAmount.textContent = amount.toLocaleString('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            }
        }
    });
</script>
{% endblock %}
