{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}تعديل بيانات المستخدم - لوحة التحكم{% endblock %}

{% block body_class %}admin-users{% endblock %}

{% block content %}
<div class="section-header d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-edit me-2"></i> تعديل بيانات المستخدم</h2>
    <a href="{% url 'admin_users' %}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة المستخدمين
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow-sm">
            <div class="card-body">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="POST" class="admin-form">
                    {% csrf_token %}

                    <div class="mb-4">
                        <h3 class="mb-3 text-primary">تعديل بيانات المستخدم</h3>
                        <p class="text-muted mb-4">يمكنك تعديل بيانات المستخدم من خلال النموذج أدناه</p>
                    </div>

                    <div class="alert alert-warning mb-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكد من صحة البيانات قبل حفظ التعديلات.
                    </div>

                    <!-- رقم الهوية -->
                    <div class="mb-4">
                        <label for="{{ form.id_number.id_for_label }}" class="form-label fw-bold">
                            <i class="fas fa-id-card me-1"></i>
                            رقم الهوية
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-id-card"></i>
                            </span>
                            {{ form.id_number }}
                        </div>
                        <div class="form-text text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            رقم الهوية الوطنية المكون من 9 أرقام
                        </div>
                        {% if form.id_number.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.id_number.errors %}
                                    <p class="mb-0"><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- الاسم الكامل -->
                    <div class="mb-4">
                        <label for="{{ form.full_name.id_for_label }}" class="form-label fw-bold">
                            <i class="fas fa-user me-1"></i>
                            الاسم الكامل
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-user"></i>
                            </span>
                            {{ form.full_name }}
                        </div>
                        {% if form.full_name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.full_name.errors %}
                                    <p class="mb-0"><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- المبلغ المستحق -->
                    <div class="mb-4">
                        <label for="{{ form.owed_amount.id_for_label }}" class="form-label fw-bold">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            المبلغ المستحق
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-money-bill-wave"></i>
                            </span>
                            {{ form.owed_amount }}
                            <span class="input-group-text bg-light">شيقل</span>
                        </div>
                        {% if form.owed_amount.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.owed_amount.errors %}
                                    <p class="mb-0"><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <hr class="my-4">

                    <div class="d-flex justify-content-between mt-4">
                        <button type="submit" class="btn btn-primary px-4 py-2">
                            <i class="fas fa-save me-2"></i>
                            حفظ التعديلات
                        </button>
                        <a href="{% url 'admin_users' %}" class="btn btn-secondary px-4 py-2">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Set the active page for the sidebar
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to users link
        const usersLink = document.querySelector('a[href="{% url "admin_users" %}"]');
        if (usersLink) {
            usersLink.classList.add('active');
        }

        // Add validation classes to form inputs
        const idNumberInput = document.getElementById('{{ form.id_number.id_for_label }}');
        if (idNumberInput) {
            idNumberInput.classList.add('form-control');
            idNumberInput.setAttribute('placeholder', 'أدخل رقم الهوية');
        }

        const fullNameInput = document.getElementById('{{ form.full_name.id_for_label }}');
        if (fullNameInput) {
            fullNameInput.classList.add('form-control');
            fullNameInput.setAttribute('placeholder', 'أدخل الاسم الكامل');
        }

        const owedAmountInput = document.getElementById('{{ form.owed_amount.id_for_label }}');
        if (owedAmountInput) {
            owedAmountInput.classList.add('form-control');
            owedAmountInput.setAttribute('placeholder', 'أدخل المبلغ المستحق');
        }
    });
</script>
{% endblock %}
