/* Enhanced UI Styles - Professional Look and Feel */

/* ===== BACKGROUNDS ===== */
/* Main background with subtle pattern */
body {
    background-color: var(--bg-primary);
    background-image: url('../images/patterns/subtle_dots.png');
    background-attachment: fixed;
}

/* Dashboard background */
[data-theme="light"] .main-content {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(249,252,255,0.95) 100%);
    box-shadow: 0 4px 24px rgba(0,0,0,0.05);
}

[data-theme="dark"] .main-content {
    background: linear-gradient(135deg, rgba(18,18,18,0.95) 0%, rgba(30,30,30,0.95) 100%);
    box-shadow: 0 4px 24px rgba(0,0,0,0.2);
}

/* Sidebar with gradient */
[data-theme="light"] .sidebar {
    background: linear-gradient(180deg, #22304a 0%, #1a2539 100%);
}

[data-theme="dark"] .sidebar {
    background: linear-gradient(180deg, #0a0a0a 0%, #000000 100%);
}

/* ===== ENHANCED DATATABLES ===== */
/* Search and length controls container */
.dataTables_wrapper .dt-controls {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding: 1rem;
    border-radius: 10px;
    background: var(--bg-secondary);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

[data-theme="dark"] .dataTables_wrapper .dt-controls {
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Search box styling */
.dataTables_wrapper .dataTables_filter {
    display: flex;
    align-items: center;
    position: relative;
    margin: 0;
}

.dataTables_wrapper .dataTables_filter label {
    display: flex;
    align-items: center;
    margin: 0;
    width: 100%;
    font-weight: bold;
}

.dataTables_wrapper .dataTables_filter input {
    margin: 0 0 0 0.5rem;
    padding: 0.6rem 1rem 0.6rem 2.5rem;
    border-radius: 50px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--text-primary);
    width: 250px;
    transition: all 0.3s ease;
}

.dataTables_wrapper .dataTables_filter input:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
    border-color: #2563eb;
    outline: none;
    width: 300px;
}

/* Add search icon */
.dataTables_wrapper .dataTables_filter::before {
    content: "\f002";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    z-index: 1;
}

/* Length (show entries) styling */
.dataTables_wrapper .dataTables_length {
    display: flex;
    align-items: center;
    margin: 0;
}

.dataTables_wrapper .dataTables_length label {
    display: flex;
    align-items: center;
    margin: 0;
    font-weight: bold;
}

.dataTables_wrapper .dataTables_length select {
    margin: 0 0.5rem;
    padding: 0.5rem 2rem 0.5rem 1rem;
    border-radius: 50px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--text-primary);
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c757d'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.7rem center;
    background-size: 1.2rem;
}

.dataTables_wrapper .dataTables_length select:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
    border-color: #2563eb;
    outline: none;
}

/* Pagination styling */
.dataTables_wrapper .dataTables_paginate {
    margin-top: 1.5rem;
    display: flex;
    justify-content: center;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.5rem 0.8rem;
    margin: 0 0.2rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    color: var(--text-primary) !important;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%);
    color: white !important;
    border-color: #2563eb;
    font-weight: bold;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover:not(.current) {
    background-color: var(--hover-bg);
    color: var(--text-primary) !important;
    border-color: var(--border-color);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Info (Showing X to Y of Z entries) styling */
.dataTables_wrapper .dataTables_info {
    margin-top: 1.5rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    text-align: center;
    font-size: 0.9rem;
}

/* ===== CARD ENHANCEMENTS ===== */
.card {
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

[data-theme="dark"] .card:hover {
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.card-header {
    padding: 1.2rem 1.5rem;
    background: linear-gradient(90deg, rgba(37,99,235,0.1) 0%, rgba(37,99,235,0.05) 100%);
    border-bottom: 1px solid rgba(37,99,235,0.1);
}

[data-theme="dark"] .card-header {
    background: linear-gradient(90deg, rgba(37,99,235,0.2) 0%, rgba(37,99,235,0.1) 100%);
    border-bottom: 1px solid rgba(37,99,235,0.2);
}

.card-header h5, .card-header h6 {
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
}

.card-header h5 i, .card-header h6 i {
    margin-left: 0.5rem;
    color: #2563eb;
}

/* ===== TABLE ENHANCEMENTS ===== */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.table th {
    background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 1rem;
}

.table th:first-child {
    border-top-right-radius: 10px;
}

.table th:last-child {
    border-top-left-radius: 10px;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(37,99,235,0.05);
}

[data-theme="dark"] .table tbody tr:hover {
    background-color: rgba(37,99,235,0.15);
}

/* ===== BUTTON ENHANCEMENTS ===== */
.btn {
    border-radius: 50px;
    padding: 0.6rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(90deg, #ef4444 0%, #f87171 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(90deg, #0ea5e9 0%, #38bdf8 100%);
    border: none;
}

/* ===== FORM CONTROL ENHANCEMENTS ===== */
.form-control, .form-select {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    box-shadow: 0 0 0 3px rgba(37,99,235,0.2);
    border-color: #2563eb;
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
    .dataTables_wrapper .dt-controls {
        flex-direction: column;
        gap: 1rem;
    }
    
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_length {
        width: 100%;
    }
    
    .dataTables_wrapper .dataTables_filter input {
        width: 100%;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        width: 100%;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.main-content {
    animation: fadeIn 0.5s ease-out;
}

.card {
    animation: fadeIn 0.5s ease-out;
}

/* Stagger animation for cards in a row */
.row .card:nth-child(1) { animation-delay: 0.1s; }
.row .card:nth-child(2) { animation-delay: 0.2s; }
.row .card:nth-child(3) { animation-delay: 0.3s; }
.row .card:nth-child(4) { animation-delay: 0.4s; }
