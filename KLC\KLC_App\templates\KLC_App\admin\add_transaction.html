{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}إضافة طلب جديد - لوحة التحكم{% endblock %}

{% block body_class %}admin-transactions{% endblock %}

{% block extra_css %}
<style>
    /* Professional form styling */
    .icon-circle {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(58, 124, 165, 0.1);
    }

    .bg-info-light {
        background-color: rgba(33, 150, 243, 0.1) !important;
    }

    .bg-warning-light {
        background-color: rgba(255, 193, 7, 0.1) !important;
    }

    .bg-primary-light {
        background-color: rgba(58, 124, 165, 0.1) !important;
    }

    .input-group-focus {
        box-shadow: 0 0 0 0.25rem rgba(58, 124, 165, 0.25) !important;
        transition: all 0.2s ease-in-out;
    }

    .input-group {
        transition: all 0.2s ease-in-out;
    }

    .form-control:focus {
        box-shadow: none !important;
    }

    .professional-form {
        border-radius: 12px;
        overflow: hidden;
        padding: 2rem;
    }

    /* Section header styling */
    .section-icon-circle {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(58, 124, 165, 0.1);
    }

    .section-header h2 {
        color: #2c5d7c;
        font-weight: 600;
    }

    .btn-outline-primary {
        color: #3a7ca5;
        border-color: #3a7ca5;
    }

    .btn-outline-primary:hover {
        background-color: #3a7ca5;
        color: white;
    }

    /* Animation for form elements */
    .form-group {
        animation: fadeInUp 0.5s ease-out forwards;
        opacity: 0;
    }

    .form-group:nth-child(1) {
        animation-delay: 0.1s;
    }

    .form-group:nth-child(2) {
        animation-delay: 0.2s;
    }

    .form-group:nth-child(3) {
        animation-delay: 0.3s;
    }

    .form-group:nth-child(4) {
        animation-delay: 0.4s;
    }

    .form-group:nth-child(5) {
        animation-delay: 0.5s;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @media (max-width: 768px) {
        .professional-form {
            padding: 1.25rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="section-header d-flex justify-content-between align-items-center mb-4">
    <div class="d-flex align-items-center">
        <div class="section-icon-circle me-3 bg-primary-light">
            <i class="fas fa-file-alt text-primary"></i>
        </div>
        <h2 class="m-0">إضافة طلب جديد</h2>
    </div>
    <a href="{% url 'admin_transactions' %}" class="btn btn-outline-primary rounded-pill px-4">
        <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة الطلبات
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow professional-form">
            <div class="card-body p-4">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Alert container for transaction messages -->
                <div id="transactionAlertContainer" class="mb-3" style="display: none;">
                    <div id="transactionAlert" class="alert alert-warning text-center"></div>
                </div>

                <form id="transactions-form" action="{% url 'admin_make_transaction' %}" method="POST" class="admin-form">
                    {% csrf_token %}

                    <div class="form-header text-center mb-4">
                        <div class="icon-circle bg-primary-light mb-3 mx-auto">
                            <i class="fas fa-file-signature text-primary fa-2x"></i>
                        </div>
                        <h3 class="fw-bold text-primary">طلب معاملة جديدة</h3>
                        <p class="text-muted">يرجى إدخال بيانات المعاملة بدقة</p>
                    </div>

                    <div class="alert alert-warning mb-4 border-0 bg-warning-light">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-exclamation-triangle text-warning fa-lg"></i>
                            </div>
                            <div>
                                تنويه: عند إختيارك لنوع الطلب، يرجى قراءة التعليمات في الأسفل قبل إرسال الطلب.
                            </div>
                        </div>
                    </div>
                    
                    <!-- رقم الهوية -->
                    <div class="form-group mb-4">
                        <label for="transactionsIdNumber" class="form-label fw-bold mb-2">
                            <i class="fas fa-id-card me-1 text-primary"></i>
                            رقم الهوية
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-id-card text-primary"></i>
                            </span>
                            <input
                                type="text"
                                class="form-control border-start-0"
                                id="transactionsIdNumber"
                                name="transactionsIdNumber"
                                placeholder="أدخل رقم الهوية"
                                required
                                title="رقم الهوية"
                            />
                        </div>
                    </div>
                    
                    <!-- الإسم الرباعي -->
                    <div class="form-group mb-4">
                        <label for="transactionsFullName" class="form-label fw-bold mb-2">
                            <i class="fas fa-user me-1 text-primary"></i>
                            الإسم الرباعي (حسب الهوية)
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-user text-primary"></i>
                            </span>
                            <input
                                type="text"
                                class="form-control border-start-0"
                                id="transactionsFullName"
                                name="transactionsFullName"
                                placeholder="أدخل الاسم الرباعي"
                                required
                                pattern="^[\u0621-\u064A\u0660-\u0669a-zA-Z\s]+$"
                                title="الإسم الرباعي يجب أن يتكون من حروف فقط"
                                oninput="this.value = this.value.replace(/[^a-zA-Z\u0621-\u064A\s]/g, '')"
                            />
                        </div>
                    </div>
                    
                    <!-- رقم الهاتف -->
                    <div class="form-group mb-4">
                        <label for="transactionsPhoneNumber" class="form-label fw-bold mb-2">
                            <i class="fas fa-phone-alt me-1 text-primary"></i>
                            رقم الهاتف
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-phone-alt text-primary"></i>
                            </span>
                            <input
                                type="tel"
                                class="form-control border-start-0 telefone-input"
                                id="transactionsPhoneNumber"
                                name="transactionsPhoneNumber"
                                placeholder="مثال:0592345678 أو مع رقم مفتاح الدولة"
                                required
                                minlength="10"
                                maxlength="15"
                                oninput="this.value = this.value.replace(/[^0-9+]/g, '')"
                                pattern="\d{10, 15}"
                                title="رقم الهاتف يجب أن يتكون من أرقام فقط"
                            />
                        </div>
                    </div>
                    
                    <!-- نوع المعاملة -->
                    <div class="form-group mb-4">
                        <label for="transactionType" class="form-label fw-bold mb-2">
                            <i class="fas fa-tasks me-1 text-primary"></i>
                            نوع المعاملة
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-tasks text-primary"></i>
                            </span>
                            <select
                                class="form-select border-start-0"
                                id="transactionType"
                                name="transactionType"
                                required
                            >
                                <option value="" disabled selected hidden>
                                    إختر نوع المعاملة
                                </option>
                                <option value="Route Marking">طلب تعليم طريق</option>
                                <option value="Site Plan Guide">طلب دليل مخطط موقع</option>
                                <option value="Building Permit">طلب ترخيص مبنى</option>
                                <option value="Water Line NOC">
                                    طلب عدم ممانعة لتقديم خدمة خط مياه
                                </option>
                                <option value="Electricity Line NOC">
                                    طلب عدم ممانعة لمد خط كهرباء
                                </option>
                                <option value="Proof of Residence">طلب إثبات سكن</option>
                                <option value="Employment Proof">طلب إثبات عمل</option>
                                <option
                                    value="Request for Sale Transaction (for holders of Palestinian ID) – Plot Description and Clearance Certificate"
                                >
                                    طلب معاملات صفقة بيع (لحملة الهوية الفلسطينية) وصف قطعة، براءة
                                    ذمة
                                </option>
                                <option
                                    value="Request for Sale Transaction (for holders of Jerusalem ID) – Plot Description, Clearance Certificate, and Classification"
                                >
                                    طلب معاملات صفقة بيع (لحملة هوية القدس) وصف قطعة، براءة ذمة
                                    وتصنيف
                                </option>
                                <option value="Clearance for Kushan">
                                    طلب معاملة براءة ذمة لاستخراج الكوشان
                                </option>
                                <option value="other">طلب معاملة أخرى او استفسار</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- ملاحظات إضافية -->
                    <div class="form-group mb-4">
                        <label for="transactionAdditionalNotes" class="form-label fw-bold mb-2">
                            <i class="fas fa-comment-alt me-1 text-primary"></i>
                            ملاحظات إضافية
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-comment-alt text-primary"></i>
                            </span>
                            <textarea
                                class="form-control border-start-0"
                                id="transactionAdditionalNotes"
                                name="transactionAdditionalNotes"
                                rows="3"
                                placeholder="اكتب ملاحظاتك هنا... (إختياري)"
                            ></textarea>
                        </div>
                    </div>
                    
                    <!-- تعليمات المعاملة -->
                    <div
                        id="transactionNotes"
                        class="alert alert-warning mb-4 border-0 bg-warning-light p-3 rounded"
                    ></div>
                    
                    <!--Get the council contact info text-->
                    {% include 'KLC_App/council_contact_info_for_services.html' %}

                    <hr class="my-4">
                    
                    <div class="d-flex justify-content-between mt-4">
                        <button type="submit" class="btn btn-primary btn-lg px-5 py-2 shadow-sm">
                            <i class="fas fa-paper-plane me-2"></i>
                            إرسال الطلب
                        </button>
                        <a href="{% url 'admin_transactions' %}" class="btn btn-outline-secondary btn-lg px-5 py-2">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Set the active page for the sidebar
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to transactions link
        const transactionsLink = document.querySelector('a[href="{% url "admin_transactions" %}"]');
        if (transactionsLink) {
            transactionsLink.classList.add('active');
        }

        // Add focus effects for professional look
        const formInputs = document.querySelectorAll('.form-control, .form-select');
        formInputs.forEach(input => {
            // Add focus effect
            input.addEventListener('focus', function() {
                this.closest('.input-group').classList.add('input-group-focus');
            });

            // Remove focus effect
            input.addEventListener('blur', function() {
                this.closest('.input-group').classList.remove('input-group-focus');
            });
        });

        // Initialize the transaction form notes display
        displayTransactionFormNotes();

        // Form submission handler
        const form = document.getElementById('transactions-form');
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitTransactionForm(e);
        });
    });

    // Function to handle form submission
    function submitTransactionForm(event) {
        event.preventDefault();

        const form = document.getElementById('transactions-form');
        const formData = new FormData(form);
        const alertContainer = document.getElementById('transactionAlertContainer');
        const alertElement = document.getElementById('transactionAlert');

        // Hide any previous alerts
        alertContainer.style.display = 'none';

        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status && data.status !== 'success') {
                // Show the alert with the error message
                alertElement.textContent = data.status;
                alertContainer.style.display = 'block';

                // Scroll to the top of the form to show the alert
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            } else {
                // Success - redirect to admin transactions page
                window.location.href = '{% url "admin_transactions" %}';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Fallback to normal form submission if fetch fails
            form.submit();
        });
    }

    function displayTransactionFormNotes() {
        // Get the transaction type select element and notes paragraph
        const transactionTypeSelect = document.getElementById("transactionType");
        const transactionNotes = document.getElementById("transactionNotes");
        /// Map contains the notes for each transaction type
        /////////////// Maybe the following will be changed as KLC owners needs **** Sholud be discussed with them
        const transactionNotesMap = {
            "Route Marking":
                "الرجاء تحديد المسافة المطلوبة بالمتر (الطول والعرض) وذلك في خانة الملاحظات -\n" +
                "عند قدومك للمجلس لتقديم طلب تعليم طريق، يرجى إحضار الوثائق التالية:\n" +
                "- براءة الذمة المالية من المجلس\n" +
                "- صورة حديثة عن هوية مقدم الطلب أو صاحب الأرض أو الوكيل القانوني\n" +
                "- صورة عن كوشان قطعة الأرض\n" +
                "(الرسوم المطلوبة: 200 شيكل)",

            "Site Plan Guide":
                "عند قدومك للمجلس لتقديم طلب دليل مخطط موقع، يرجى إحضار الوثائق التالية: - صورة عن كوشان قطعة الأرض - صورة عن هوية المالك سارية المفعول (الرسوم المطلوبة: 20 دينار)",

            "Building Permit":
                "عند قدومك للمجلس لتقديم طلب ترخيص مبنى، يرجى إحضار الوثائق التالية: - دليل مخطط موقع معتمد - صورة عن هوية المالك سارية المفعول - صورة عن كوشان قطعة الأرض (الرسوم تحدد حسب تصنيف الأرض ومساحة المبنى وعدد الطوابق)",

            "Water Line NOC":
                "عند قدومك للمجلس لتقديم طلب عدم ممانعة لخط مياه، يرجى إحضار الوثائق التالية: - براءة الذمة المالية من المجلس - صورة عن هوية المشترك سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

            "Electricity Line NOC":
                "عند قدومك للمجلس لتقديم طلب عدم ممانعة لخط كهرباء، يرجى إحضار الوثائق التالية: - رخصة البناء - براءة الذمة المالية من المجلس - صورة عن هوية المشترك سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

            "Proof of Residence":
                "عند قدومك للمجلس لتقديم طلب إثبات سكن، يرجى إحضار الوثائق التالية: - براءة الذمة المالية من المجلس - صورة عن الهوية الشخصية سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

            "Employment Proof":
                "عند قدومك للمجلس لتقديم طلب إثبات عمل، يرجى إحضار الوثائق التالية: - براءة الذمة المالية من المجلس - صورة عن الهوية الشخصية سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

            "Request for Sale Transaction (for holders of Palestinian ID) – Plot Description and Clearance Certificate":
                "عند قدومك للمجلس لتقديم طلب صفقة بيع (لحملة الهوية الفلسطينية)، يرجى إحضار الوثائق التالية: - براءة الذمة المالية لقطعة الأرض ومالكها - صورة عن هوية المالك سارية المفعول - كوشان قطعة الأرض (الرسوم المطلوبة: 50 شيكل للبراءة + 100 شيكل لوصف القطعة)",

            "Request for Sale Transaction (for holders of Jerusalem ID) – Plot Description, Clearance Certificate, and Classification":
                "عند قدومك للمجلس لتقديم طلب صفقة بيع (لحملة هوية القدس)، يرجى إحضار الوثائق التالية: - براءة الذمة المالية لقطعة الأرض ومالكها - صورة عن هوية المالك سارية المفعول - كوشان قطعة الأرض (الرسوم المطلوبة: 50 شيكل للبراءة + 100 شيكل لوصف القطعة + 100 شيكل للتصنيف)",

            "Clearance for Kushan":
                "عند قدومك للمجلس لتقديم طلب براءة ذمة لاستخراج الكوشان، يرجى إحضار الوثائق التالية: - براءة الذمة المالية لقطعة الأرض ومالكها - صورة عن هوية المالك سارية المفعول (الرسوم المطلوبة: 20 شيكل)",

            other:
                "عند تقديم طلب معاملة أخرى أو إستفسار يجب عليك كتابة الطلب أو الإستفسار في قسم الملاحظات الإضافية.",
        };

        // Default note
        const defaultNote =
            "*يرجى إختيار نوع المعاملة من القائمة أعلاه لتظهر لك التعليمات الخاصة بها هنا.";

        // Set initial note
        transactionNotes.textContent = defaultNote;

        // Add event listener for changes
        transactionTypeSelect.addEventListener("change", function () {
            const selectedType = this.value;
            if (selectedType && transactionNotesMap[selectedType]) {
                transactionNotes.textContent = transactionNotesMap[selectedType];
            } else {
                transactionNotes.textContent = defaultNote;
            }
        });
    }
</script>
{% endblock %}
