// News Detail Modal Functionality
document.addEventListener("DOMContentLoaded", function () {
  // News Detail Modal
  const newsDetailModal = document.getElementById("newsDetailModal");
  if (newsDetailModal) {
    newsDetailModal.addEventListener("show.bs.modal", function (event) {
      const button = event.relatedTarget;
      const newsTitle = button.getAttribute("data-news-title");
      const newsDesc = button.getAttribute("data-news-desc");
      const newsDate = button.getAttribute("data-news-date");
      const newsImage = button.getAttribute("data-news-image");
      const newsCategory = button.getAttribute("data-news-category");
      const newsVideo = button.getAttribute("data-news-video");

      const modalTitle = newsDetailModal.querySelector("#newsDetailModalLabel");
      const modalImage = newsDetailModal.querySelector("#newsDetailImage");
      const modalDate = newsDetailModal.querySelector("#newsDetailDate");
      const modalCategory = newsDetailModal.querySelector("#newsDetailCategory");
      const modalDesc = newsDetailModal.querySelector("#newsDetailDescription");
      const videoContainer = newsDetailModal.querySelector("#newsDetailVideoContainer");
      const showVideoBtn = newsDetailModal.querySelector("#showVideoBtn");

      // Set modal content
      if (modalTitle) modalTitle.textContent = newsTitle || '';
      if (modalImage) {
        modalImage.src = newsImage || '';
        modalImage.alt = newsTitle || '';
      }
      if (modalDate) modalDate.textContent = newsDate || '';
      if (modalCategory) modalCategory.textContent = newsCategory || '';
      if (modalDesc) modalDesc.textContent = newsDesc || '';

      // Handle video button visibility
      if (showVideoBtn) {
        if (newsVideo && newsVideo.trim() !== '') {
          showVideoBtn.style.display = 'inline-block';
          showVideoBtn.setAttribute('data-last-video', newsVideo);

          // Add click event to show video
          showVideoBtn.onclick = function() {
            // Check if video URL is from Google Drive
            if (newsVideo.includes("drive.google.com")) {
              let fileId = "";
              if (newsVideo.includes("/file/d/")) {
                fileId = newsVideo.split("/file/d/")[1].split("/")[0];
              } else if (newsVideo.includes("id=")) {
                fileId = newsVideo.split("id=")[1].split("&")[0];
              }

              if (fileId) {
                const embedUrl = `https://drive.google.com/file/d/${fileId}/preview`;
                videoContainer.innerHTML = `
                  <div class="video-container mt-4">
                    <iframe src="${embedUrl}" width="100%" height="400" allowfullscreen frameborder="0"></iframe>
                  </div>`;
                showVideoBtn.style.display = 'none';
              }
            } else {
              videoContainer.innerHTML = `
                <div class="video-container mt-4">
                  <iframe src="${newsVideo}" width="100%" height="400" allowfullscreen frameborder="0"></iframe>
                </div>`;
              showVideoBtn.style.display = 'none';
            }
          };
        } else {
          // No video available, hide the button
          showVideoBtn.style.display = 'none';
        }
      }
    });

    // Clear video when modal is closed
    newsDetailModal.addEventListener("hidden.bs.modal", function () {
      const videoContainer = newsDetailModal.querySelector("#newsDetailVideoContainer");
      if (videoContainer) {
        videoContainer.innerHTML = ""; // Clear video when modal is closed
      }

      const showVideoBtn = newsDetailModal.querySelector("#showVideoBtn");
      if (showVideoBtn) {
        // Reset button visibility based on whether there's a video
        const lastVideoUrl = showVideoBtn.getAttribute("data-last-video");
        if (lastVideoUrl && lastVideoUrl.trim() !== '') {
          showVideoBtn.style.display = 'inline-block';
        } else {
          showVideoBtn.style.display = 'none';
        }
      }
    });
  }

  // Video Modal
  const videoModal = document.getElementById("videoModal");
  if (videoModal) {
    videoModal.addEventListener("show.bs.modal", function (event) {
      const button = event.relatedTarget;
      const videoSrc = button.getAttribute("data-video-src");
      const newsTitle = button.getAttribute("data-news-title");
      const newsDesc = button.getAttribute("data-news-desc");
      const newsDate = button.getAttribute("data-news-date");

      const modalTitle = videoModal.querySelector(".modal-title");
      const modalDesc = videoModal.querySelector(".modal-description");
      const modalDate = videoModal.querySelector(".modal-date span");
      const iframe = videoModal.querySelector("iframe");

      if (modalTitle) modalTitle.textContent = newsTitle || 'فيديو توضيحي';
      if (modalDesc) modalDesc.textContent = newsDesc || '';
      if (modalDate) modalDate.textContent = newsDate || '';

      // Process video URL
      if (videoSrc && videoSrc.trim() !== '') {
        // Check if video URL is from Google Drive
        if (videoSrc.includes("drive.google.com")) {
          let fileId = "";
          if (videoSrc.includes("/file/d/")) {
            fileId = videoSrc.split("/file/d/")[1].split("/")[0];
          } else if (videoSrc.includes("id=")) {
            fileId = videoSrc.split("id=")[1].split("&")[0];
          }

          if (fileId && iframe) {
            const embedUrl = `https://drive.google.com/file/d/${fileId}/preview`;
            iframe.src = embedUrl;
          } else if (iframe) {
            iframe.src = videoSrc;
          }
        } else if (iframe) {
          iframe.src = videoSrc;
        }
      }
    });

    // Clear video when modal is closed
    videoModal.addEventListener("hidden.bs.modal", function () {
      const iframe = videoModal.querySelector("iframe");
      if (iframe) {
        iframe.src = ""; // Stop the video
      }
    });
  }
});
