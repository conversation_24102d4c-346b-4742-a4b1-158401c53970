{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}إضافة حجز جديد - لوحة التحكم{% endblock %}

{% block body_class %}admin-reservations{% endblock %}

{% block extra_css %}
<!-- Flatpickr CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

<style>
    /* Professional form styling */
    .icon-circle {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(58, 124, 165, 0.1);
    }

    .bg-info-light {
        background-color: rgba(33, 150, 243, 0.1) !important;
    }

    .bg-primary-light {
        background-color: rgba(58, 124, 165, 0.1) !important;
    }

    .input-group-focus {
        box-shadow: 0 0 0 0.25rem rgba(58, 124, 165, 0.25) !important;
        transition: all 0.2s ease-in-out;
    }

    .input-group {
        transition: all 0.2s ease-in-out;
    }

    .form-control:focus {
        box-shadow: none !important;
    }

    .professional-form {
        border-radius: 12px;
        overflow: hidden;
    }

    /* Section header styling */
    .section-icon-circle {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(58, 124, 165, 0.1);
    }

    .section-header h2 {
        color: #2c5d7c;
        font-weight: 600;
    }

    .btn-outline-primary {
        color: #3a7ca5;
        border-color: #3a7ca5;
    }

    .btn-outline-primary:hover {
        background-color: #3a7ca5;
        color: white;
    }

    /* Animation for form elements */
    .form-group {
        animation: fadeInUp 0.5s ease-out forwards;
        opacity: 0;
    }

    .form-group:nth-child(1) {
        animation-delay: 0.1s;
    }

    .form-group:nth-child(2) {
        animation-delay: 0.2s;
    }

    .form-group:nth-child(3) {
        animation-delay: 0.3s;
    }

    .form-group:nth-child(4) {
        animation-delay: 0.4s;
    }

    .form-group:nth-child(5) {
        animation-delay: 0.5s;
    }

    .form-group:nth-child(6) {
        animation-delay: 0.6s;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Custom styles for flatpickr calendar */
    .flatpickr-calendar {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        font-family: 'Cairo', 'Tajawal', sans-serif;
        width: 300px !important;
        direction: rtl;
        background: #fff;
        z-index: 9999 !important;
    }

    .flatpickr-months {
        padding-top: 8px;
        padding-bottom: 8px;
        background-color: #3a7ca5;
        color: white;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }

    .flatpickr-month {
        height: 40px;
    }

    .flatpickr-current-month {
        font-size: 16px;
        font-weight: 600;
        padding-top: 0;
    }

    .flatpickr-current-month .flatpickr-monthDropdown-months {
        font-weight: 600;
    }

    .flatpickr-current-month .flatpickr-monthDropdown-months,
    .numInputWrapper {
        font-weight: 600;
        color: white;
    }

    .flatpickr-current-month .flatpickr-monthDropdown-months option {
        color: #333;
    }

    .flatpickr-weekdays {
        background-color: #f8f9fa;
        margin-top: 0;
    }

    .flatpickr-weekday {
        font-weight: 600;
        color: #555;
        height: 36px;
        line-height: 36px;
        background-color: #f8f9fa;
    }

    .flatpickr-days {
        padding: 6px;
        border: none;
    }

    .dayContainer {
        width: 100%;
        min-width: 100%;
        max-width: 100%;
    }

    .flatpickr-day {
        border-radius: 4px;
        font-weight: 500;
        font-size: 14px;
        height: 36px;
        line-height: 36px;
        margin: 2px;
        max-width: calc(100% / 7 - 4px);
        flex-basis: calc(100% / 7 - 4px);
        border: 1px solid transparent;
    }

    .flatpickr-day.selected {
        background-color: #3a7ca5;
        border-color: #3a7ca5;
        color: white;
    }

    .flatpickr-day.today {
        border-color: #3a7ca5;
        color: #3a7ca5;
        font-weight: bold;
    }

    .flatpickr-day:hover {
        background-color: #e9f7ef;
    }

    .flatpickr-day.flatpickr-disabled,
    .flatpickr-day.flatpickr-disabled:hover,
    .flatpickr-day.prevMonthDay,
    .flatpickr-day.nextMonthDay {
        color: rgba(72, 72, 72, 0.3);
        background: transparent;
    }

    .flatpickr-day.prevMonthDay,
    .flatpickr-day.nextMonthDay {
        visibility: visible !important;
        display: block !important;
    }

    /* Fix for RTL calendar navigation */
    .flatpickr-prev-month,
    .flatpickr-next-month {
        padding: 10px;
        fill: white !important;
    }

    .flatpickr-prev-month:hover,
    .flatpickr-next-month:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .flatpickr-prev-month svg,
    .flatpickr-next-month svg {
        fill: white !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="section-header d-flex justify-content-between align-items-center mb-4">
    <div class="d-flex align-items-center">
        <div class="section-icon-circle me-3 bg-primary-light">
            <i class="fas fa-calendar-plus text-primary"></i>
        </div>
        <h2 class="m-0">إضافة حجز جديد</h2>
    </div>
    <a href="{% url 'admin_reservations' %}" class="btn btn-outline-primary rounded-pill px-4">
        <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة الحجوزات
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow professional-form">
            <div class="card-body p-4">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form id="adminReservationForm" action="{% url 'admin_reserve_hall' %}" method="POST" class="admin-form">
                    {% csrf_token %}

                    <div class="form-header text-center mb-4">
                        <div class="icon-circle bg-primary-light mb-3 mx-auto">
                            <i class="fas fa-calendar-plus text-primary fa-2x"></i>
                        </div>
                        <h3 class="fw-bold text-primary">معلومات الحجز الجديد</h3>
                        <p class="text-muted">يرجى إدخال بيانات الحجز بدقة</p>
                    </div>

                    <div class="alert alert-info mb-4 border-0 bg-info-light">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle text-info fa-lg"></i>
                            </div>
                            <div>
                                جميع الحقول المميزة بعلامة <span class="text-danger fw-bold">*</span> هي حقول إلزامية.
                            </div>
                        </div>
                    </div>
                    
                    <!-- رقم الهوية -->
                    <div class="form-group mb-4">
                        <label for="idNumber" class="form-label fw-bold mb-2">
                            <i class="fas fa-id-card me-1 text-primary"></i>
                            رقم الهوية
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-id-card text-primary"></i>
                            </span>
                            <input type="text" class="form-control border-start-0" id="idNumber" name="idNumber" placeholder="أدخل رقم الهوية" required>
                        </div>
                    </div>
                    
                    <!-- الاسم -->
                    <div class="form-group mb-4">
                        <label for="fullName" class="form-label fw-bold mb-2">
                            <i class="fas fa-user me-1 text-primary"></i>
                            الاسم
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-user text-primary"></i>
                            </span>
                            <input type="text" class="form-control border-start-0" id="fullName" name="fullName" placeholder="أدخل الاسم الكامل" required>
                        </div>
                    </div>
                    
                    <!-- رقم الهاتف -->
                    <div class="form-group mb-4">
                        <label for="phoneNumber" class="form-label fw-bold mb-2">
                            <i class="fas fa-phone-alt me-1 text-primary"></i>
                            رقم الهاتف
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-phone-alt text-primary"></i>
                            </span>
                            <input type="text" class="form-control border-start-0" id="phoneNumber" name="phoneNumber" placeholder="أدخل رقم الهاتف" required>
                        </div>
                    </div>
                    
                    <!-- نوع المناسبة -->
                    <div class="form-group mb-4">
                        <label for="eventType" class="form-label fw-bold mb-2">
                            <i class="fas fa-glass-cheers me-1 text-primary"></i>
                            نوع المناسبة
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-glass-cheers text-primary"></i>
                            </span>
                            <select class="form-select border-start-0" id="eventType" name="eventType" required>
                                <option value="" disabled selected>اختر نوع المناسبة</option>
                                <option value="عرس">عرس</option>
                                <option value="طلبة عريس للرجال">طلبة عريس للرجال</option>
                                <option value="خطوبة للنساء">خطوبة للنساء</option>
                                <option value="حفل حناء عروس">حفل حناء عروس</option>
                                <option value="حفل تخرج (توجيهي أو جامعة)">
                                    حفل تخرج (توجيهي أو جامعة)
                                </option>
                                <option value="غداء عرس">غداء عرس</option>
                                <option value="ورشات عمل">ورشات عمل</option>
                                <option value="بيوت أجر">بيوت أجر</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- تاريخ البداية -->
                    <div class="form-group mb-4">
                        <label for="startDateInput" class="form-label fw-bold mb-2">
                            <i class="fas fa-calendar me-1 text-primary"></i>
                            تاريخ البداية
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-calendar-alt text-primary"></i>
                            </span>
                            <input type="text" class="form-control border-start-0 flatpickr-date" id="startDateInput" name="startDate" placeholder="اختر تاريخ البداية" autocomplete="off" required>
                        </div>
                    </div>
                    
                    <!-- تاريخ النهاية -->
                    <div class="form-group mb-4">
                        <label for="endDateInput" class="form-label fw-bold mb-2">
                            <i class="fas fa-calendar-check me-1 text-primary"></i>
                            تاريخ النهاية
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-calendar-check text-primary"></i>
                            </span>
                            <input type="text" class="form-control border-start-0 flatpickr-date" id="endDateInput" name="endDate" placeholder="اختر تاريخ النهاية" autocomplete="off" required disabled>
                        </div>
                    </div>

                    <hr class="my-4">
                    
                    <div class="d-flex justify-content-between mt-4">
                        <button type="submit" class="btn btn-primary btn-lg px-5 py-2 shadow-sm">
                            <i class="fas fa-save me-2"></i>
                            حفظ الحجز
                        </button>
                        <a href="{% url 'admin_reservations' %}" class="btn btn-outline-secondary btn-lg px-5 py-2">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Flatpickr JS -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<!-- Flatpickr Arabic Language -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>

<script>
    // Set the active page for the sidebar
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to reservations link
        const reservationsLink = document.querySelector('a[href="{% url "admin_reservations" %}"]');
        if (reservationsLink) {
            reservationsLink.classList.add('active');
        }

        // Add focus effects for professional look
        const formInputs = document.querySelectorAll('.form-control, .form-select');
        formInputs.forEach(input => {
            // Add focus effect
            input.addEventListener('focus', function() {
                this.closest('.input-group').classList.add('input-group-focus');
            });

            // Remove focus effect
            input.addEventListener('blur', function() {
                this.closest('.input-group').classList.remove('input-group-focus');
            });
        });

        // Calendar configuration
        // Calculate dates
        const today = new Date();

        // Start date should be at least 4 days in the future
        const minSelectableDate = new Date();
        minSelectableDate.setDate(today.getDate() + 4);

        // Calculate end of year for max date
        const currentYear = today.getFullYear();
        const nextYear = today.getMonth() > 10 ? currentYear + 1 : currentYear;
        const endOfYear = new Date(nextYear, 11, 31);

        // Common flatpickr config options
        const commonConfig = {
            dateFormat: "Y-m-d", // YYYY-MM-DD format for backend compatibility
            locale: "ar",
            disableMobile: true,
            static: false,
            monthSelectorType: "dropdown",
            showMonths: 1,
            position: "auto",
            ariaDateFormat: "Y-m-d",
            animate: true,
            showDaysInNextAndPreviousMonths: true,
            fixedHeight: true,
            prevArrow: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"></path></svg>',
            nextArrow: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg>'
        };

        // Initialize start date picker with professional styling
        const startDatePicker = flatpickr("#startDateInput", {
            ...commonConfig,
            minDate: minSelectableDate,
            maxDate: endOfYear,
            onChange: function (selectedDates, dateStr) {
                if (selectedDates.length > 0) {
                    // Enable end date picker once start date is selected
                    const endDateInput = document.getElementById('endDateInput');
                    endDateInput.disabled = false;
                    endDateInput.placeholder = "اختر تاريخ النهاية";

                    // Set min/max range for end date (start date to start date + 7 days)
                    const startDate = selectedDates[0];
                    const minEndDate = new Date(startDate);
                    const maxEndDate = new Date(startDate);
                    maxEndDate.setDate(startDate.getDate() + 7);

                    // Ensure max end date doesn't exceed end of year
                    if (maxEndDate > endOfYear) {
                        maxEndDate.setTime(endOfYear.getTime());
                    }

                    // Update end date picker constraints
                    endDatePicker.set('minDate', minEndDate);
                    endDatePicker.set('maxDate', maxEndDate);
                    endDatePicker.clear(); // Clear any previously selected end date

                    // Open the end date picker automatically after a short delay
                    setTimeout(() => {
                        endDatePicker.open();
                    }, 300);
                }
            }
        });

        // Initialize end date picker
        const endDatePicker = flatpickr("#endDateInput", {
            ...commonConfig,
            onOpen: function () {
                // If no start date is selected, close the end date picker
                if (!startDatePicker.selectedDates.length) {
                    this.close();
                    return;
                }
            }
        });

        // Form submission handler
        const form = document.getElementById('adminReservationForm');
        if (form) {
            form.addEventListener('submit', function (e) {
                e.preventDefault();

                // Ensure dates are in the correct format
                const startDateInput = document.getElementById('startDateInput');
                const endDateInput = document.getElementById('endDateInput');

                // Validate dates before submission
                if (!startDateInput.value) {
                    alert('الرجاء اختيار تاريخ البداية');
                    return;
                }

                if (!endDateInput.value) {
                    alert('الرجاء اختيار تاريخ النهاية');
                    return;
                }

                // Create FormData and ensure dates are in YYYY-MM-DD format
                const formData = new FormData(form);

                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        window.location.href = '{% url "admin_reservations" %}';
                    } else {
                        alert('خطأ: ' + data.status);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء إرسال البيانات. الرجاء المحاولة مرة أخرى.');
                });
            });
        }
    });
</script>
{% endblock %}
