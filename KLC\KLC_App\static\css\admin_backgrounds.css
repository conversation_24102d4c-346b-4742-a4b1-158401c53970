/* Admin Backgrounds CSS - Professional Style */

/* Base background for all admin pages - Similar to services page but more professional */
body {
    position: relative;
    background-color: #f5f7fa;
}

/* Add overlay to make content more readable */
body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.92) 0%, rgba(17, 24, 39, 0.95) 100%);
    z-index: -1;
}

/* Dark mode adjustments */
[data-theme="dark"] body::before {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.94) 0%, rgba(3, 7, 18, 0.97) 100%);
}

/* Add subtle radial gradient for more depth */
body {
    background: radial-gradient(circle at center, rgba(255,255,255,0.03) 0%, rgba(0,0,0,0.1) 100%);
}

[data-theme="dark"] body {
    background: radial-gradient(circle at center, rgba(255,255,255,0.02) 0%, rgba(0,0,0,0.2) 100%);
}

/* Main content area with glass effect */
.main-content {
    background: rgba(255, 255, 255, 0.85) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Dark mode main content */
[data-theme="dark"] .main-content {
    background: rgba(30, 41, 59, 0.85) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Cards with glass effect */
.card {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dark mode cards */
[data-theme="dark"] .card {
    background: rgba(30, 41, 59, 0.9) !important;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sidebar with glass effect */
.sidebar {
    background: linear-gradient(180deg, rgba(30, 41, 59, 0.95) 0%, rgba(17, 24, 39, 0.98) 100%) !important;
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.05);
}

/* Dark mode sidebar */
[data-theme="dark"] .sidebar {
    background: linear-gradient(180deg, rgba(15, 23, 42, 0.95) 0%, rgba(3, 7, 18, 0.98) 100%) !important;
}

/* Tables with glass effect */
.table {
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(5px);
}

/* Dark mode tables */
[data-theme="dark"] .table {
    background: rgba(30, 41, 59, 0.8) !important;
}

/* DataTables controls with glass effect */
.dataTables_wrapper .dt-controls {
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dark mode DataTables controls */
[data-theme="dark"] .dataTables_wrapper .dt-controls {
    background: rgba(30, 41, 59, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Add subtle pattern to background */
body::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: -2;
    opacity: 0.5;
}

/* Animation for content */
.main-content {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    body::before {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(17, 24, 39, 0.98) 100%);
    }

    [data-theme="dark"] body::before {
        background: linear-gradient(135deg, rgba(15, 23, 42, 0.97) 0%, rgba(3, 7, 18, 0.99) 100%);
    }

    .main-content {
        background: rgba(255, 255, 255, 0.9) !important;
    }

    [data-theme="dark"] .main-content {
        background: rgba(30, 41, 59, 0.9) !important;
    }
}
