* [33m84d0605c[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m)[m styling admin
[31m|[m *   [33mf698bbd4[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m Add and Update: Add a new collection for news section in DB, handle the news data and load it in index page. Also, create new view to get the news data separately. Finally, add status in transactions.
[31m|[m [32m|[m[31m\[m  
[31m|[m [32m|[m[31m/[m  
[31m|[m[31m/[m[32m|[m   
* [32m|[m [33me987884c[m styling admin
* [32m|[m [33mf46eacd8[m styling
[33m|[m * [33m29e99959[m Add and Update: Add a new collection for news section in DB, handle the news data and load it in index page. Also, create new view to get the news data separately. Finally, add status in transactions.
[33m|[m[33m/[m  
* [33m519ee067[m styling
* [33m02dd66a5[m styling
* [33mc9df58cf[m styling
* [33m9324370e[m styling
* [33m859c2270[m admin modification + news section and titles in index
