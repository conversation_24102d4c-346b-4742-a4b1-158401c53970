{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}إضافة مستخدم جديد - لوحة التحكم{% endblock %}

{% block body_class %}admin-users{% endblock %}

{% block content %}
<div class="section-header d-flex justify-content-between align-items-center mb-4">
    <div class="d-flex align-items-center">
        <div class="section-icon-circle me-3 bg-primary-light">
            <i class="fas fa-user-plus text-primary"></i>
        </div>
        <h2 class="m-0">إضافة مستخدم جديد</h2>
    </div>
    <a href="{% url 'admin_users' %}" class="btn btn-outline-primary rounded-pill px-4">
        <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة المستخدمين
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow professional-form">
            <div class="card-body p-4">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="POST" class="admin-form">
                    {% csrf_token %}

                    <div class="form-header text-center mb-4">
                        <div class="icon-circle bg-primary-light mb-3 mx-auto">
                            <i class="fas fa-user-plus text-primary fa-2x"></i>
                        </div>
                        <h3 class="fw-bold text-primary">معلومات المستخدم الجديد</h3>
                        <p class="text-muted">يرجى إدخال بيانات المستخدم بدقة</p>
                    </div>

                    <div class="alert alert-info mb-4 border-0 bg-info-light">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle text-info fa-lg"></i>
                            </div>
                            <div>
                                جميع الحقول المميزة بعلامة <span class="text-danger fw-bold">*</span> هي حقول إلزامية.
                            </div>
                        </div>
                    </div>

                    <!-- رقم الهوية الوطنية -->
                    <div class="form-group mb-4">
                        <label for="{{ form.national_id.id_for_label }}" class="form-label fw-bold mb-2">
                            <i class="fas fa-id-card me-1 text-primary"></i>
                            رقم الهوية الوطنية
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-id-card text-primary"></i>
                            </span>
                            {{ form.national_id }}
                        </div>
                        <div class="form-text text-muted mt-2">
                            <i class="fas fa-info-circle me-1"></i>
                            يجب أن يكون الرقم فريداً وغير مكرر (9 أرقام)
                        </div>
                        {% if form.national_id.errors %}
                            <div class="invalid-feedback d-block mt-2">
                                {% for error in form.national_id.errors %}
                                    <p class="mb-0"><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- الاسم الرباعي -->
                    <div class="form-group mb-4">
                        <label for="{{ form.name.id_for_label }}" class="form-label fw-bold mb-2">
                            <i class="fas fa-user me-1 text-primary"></i>
                            الاسم الرباعي
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-user text-primary"></i>
                            </span>
                            {{ form.name }}
                        </div>
                        {% if form.name.errors %}
                            <div class="invalid-feedback d-block mt-2">
                                {% for error in form.name.errors %}
                                    <p class="mb-0"><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- المديونية لعام 2024 -->
                    <div class="form-group mb-4">
                        <label for="{{ form.debts_amount_2024.id_for_label }}" class="form-label fw-bold mb-2">
                            <i class="fas fa-money-bill-wave me-1 text-primary"></i>
                            المديونية لعام 2024
                            <span class="text-danger">*</span>
                        </label>
                        <div class="input-group input-group-lg shadow-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-money-bill-wave text-primary"></i>
                            </span>
                            {{ form.debts_amount_2024 }}
                            <span class="input-group-text bg-white border-start-0">شيقل</span>
                        </div>
                        {% if form.debts_amount_2024.errors %}
                            <div class="invalid-feedback d-block mt-2">
                                {% for error in form.debts_amount_2024.errors %}
                                    <p class="mb-0"><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <hr class="my-4">

                    <div class="d-flex justify-content-between mt-4">
                        <button type="submit" class="btn btn-primary btn-lg px-5 py-2 shadow-sm">
                            <i class="fas fa-save me-2"></i>
                            حفظ المستخدم
                        </button>
                        <a href="{% url 'admin_users' %}" class="btn btn-outline-secondary btn-lg px-5 py-2">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Set the active page for the sidebar
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to users link
        const usersLink = document.querySelector('a[href="{% url "admin_users" %}"]');
        if (usersLink) {
            usersLink.classList.add('active');
        }

        // Add validation classes to form inputs
        const nationalIdInput = document.getElementById('{{ form.national_id.id_for_label }}');
        if (nationalIdInput) {
            nationalIdInput.classList.add('form-control', 'border-start-0');
            nationalIdInput.setAttribute('placeholder', 'أدخل رقم الهوية الوطنية');
        }

        const nameInput = document.getElementById('{{ form.name.id_for_label }}');
        if (nameInput) {
            nameInput.classList.add('form-control', 'border-start-0');
            nameInput.setAttribute('placeholder', 'أدخل الاسم الرباعي');
        }

        const debtInput = document.getElementById('{{ form.debts_amount_2024.id_for_label }}');
        if (debtInput) {
            debtInput.classList.add('form-control', 'border-start-0', 'border-end-0');
            debtInput.setAttribute('placeholder', 'أدخل قيمة المديونية');
        }

        // Add focus effects for professional look
        const formInputs = document.querySelectorAll('.form-control');
        formInputs.forEach(input => {
            // Add focus effect
            input.addEventListener('focus', function() {
                this.closest('.input-group').classList.add('input-group-focus');
            });

            // Remove focus effect
            input.addEventListener('blur', function() {
                this.closest('.input-group').classList.remove('input-group-focus');
            });
        });
    });
</script>
{% endblock %}

{% block extra_css %}
<style>
    /* Custom styles for the add user form */
    .icon-circle {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(58, 124, 165, 0.1);
    }

    .bg-info-light {
        background-color: rgba(33, 150, 243, 0.1) !important;
    }

    .bg-primary-light {
        background-color: rgba(58, 124, 165, 0.1) !important;
    }

    .input-group-focus {
        box-shadow: 0 0 0 0.25rem rgba(58, 124, 165, 0.25) !important;
        transition: all 0.2s ease-in-out;
    }

    .input-group {
        transition: all 0.2s ease-in-out;
    }

    .form-control:focus {
        box-shadow: none !important;
    }

    .professional-form {
        border-radius: 12px;
        overflow: hidden;
    }

    .btn-primary {
        background-color: #3a7ca5;
        border-color: #3a7ca5;
    }

    .btn-primary:hover {
        background-color: #2c5d7c;
        border-color: #2c5d7c;
    }

    .text-primary {
        color: #3a7ca5 !important;
    }

    /* Section header styling */
    .section-icon-circle {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(58, 124, 165, 0.1);
    }

    .section-header h2 {
        color: #2c5d7c;
        font-weight: 600;
    }

    .btn-outline-primary {
        color: #3a7ca5;
        border-color: #3a7ca5;
    }

    .btn-outline-primary:hover {
        background-color: #3a7ca5;
        color: white;
    }

    /* Animation for form elements */
    .form-group {
        animation: fadeInUp 0.5s ease-out forwards;
        opacity: 0;
    }

    .form-group:nth-child(1) {
        animation-delay: 0.1s;
    }

    .form-group:nth-child(2) {
        animation-delay: 0.2s;
    }

    .form-group:nth-child(3) {
        animation-delay: 0.3s;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}
