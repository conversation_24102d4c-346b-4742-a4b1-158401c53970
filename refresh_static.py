import os
import shutil
import subprocess
from pathlib import Path

def refresh_static_files():
    """
    Clear the staticfiles directory and run collectstatic.
    This ensures that all static files are freshly collected.
    """
    # Get the base directory
    BASE_DIR = Path(__file__).resolve().parent

    # Path to staticfiles directory
    staticfiles_dir = BASE_DIR / 'staticfiles'

    print("Refreshing static files...")

    # Check if staticfiles directory exists
    if staticfiles_dir.exists():
        print(f"Clearing {staticfiles_dir}...")

        # Remove all files in the staticfiles directory
        for item in staticfiles_dir.iterdir():
            if item.is_file():
                item.unlink()
            elif item.is_dir():
                shutil.rmtree(item)

        print("Static files directory cleared.")
    else:
        # Create the directory if it doesn't exist
        staticfiles_dir.mkdir(exist_ok=True)
        print(f"Created {staticfiles_dir} directory.")

    # Run collectstatic
    print("Running collectstatic...")
    try:
        # Use the full path to manage.py
        manage_py_path = BASE_DIR / 'manage.py'
        subprocess.run(['python', str(manage_py_path), 'collectstatic', '--noinput'], check=True)
        print("Static files collected successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error running collectstatic: {e}")
        return False

    print("Static files refresh completed.")
    return True

if __name__ == "__main__":
    refresh_static_files()
