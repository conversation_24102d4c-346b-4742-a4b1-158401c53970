/**
 * News Image Carousel
 * Handles automatic rotation of images in news cards
 * With prev/next navigation and variable rotation intervals:
 * - 15 seconds for index.html
 * - 2 minutes for news_list.html and news_detail.html
 */
document.addEventListener("DOMContentLoaded", function() {
    // Initialize all news image carousels
    initializeNewsImageCarousels();

    // Log for debugging
    console.log("News image carousels initialized with variable rotation intervals and navigation");
});

/**
 * Initialize all news image carousels on the page
 */
function initializeNewsImageCarousels() {
    // Find all news items with multiple images
    const newsCarousels = document.querySelectorAll('.news-image-carousel');

    // Initialize each carousel
    newsCarousels.forEach(carousel => {
        setupNewsCarousel(carousel);
    });
}

/**
 * Set up a single news carousel
 * @param {HTMLElement} carouselElement - The carousel container element
 */
function setupNewsCarousel(carouselElement) {
    // Get all slides in this carousel
    const slides = carouselElement.querySelectorAll('.carousel-slide');
    if (slides.length <= 1) return; // No need for carousel if only one image

    // Get indicators container
    const indicatorsContainer = carouselElement.querySelector('.carousel-indicators');

    // Check if this is a featured news carousel
    const isFeaturedCarousel = carouselElement.classList.contains('featured-news-carousel');

    // Log for debugging
    if (isFeaturedCarousel) {
        console.log("Setting up featured news carousel with", slides.length, "slides");
    }

    // Create indicators if they don't exist
    if (indicatorsContainer && slides.length > 1) {
        // Clear any existing indicators
        indicatorsContainer.innerHTML = '';

        // Create a counter display (except for featured carousel)
        if (!isFeaturedCarousel) {
            const counterDisplay = document.createElement('div');
            counterDisplay.className = 'carousel-counter';
            counterDisplay.textContent = `${1}/${slides.length}`;
            indicatorsContainer.appendChild(counterDisplay);
        }

        // Create an indicator for each slide
        for (let i = 0; i < slides.length; i++) {
            const indicator = document.createElement('button');
            indicator.className = 'carousel-indicator';
            indicator.setAttribute('aria-label', `Slide ${i + 1}`);
            indicator.style.cursor = 'pointer';

            // Add a title attribute for better accessibility
            indicator.setAttribute('title', `عرض الصورة ${i + 1}`);

            if (i === 0) {
                indicator.classList.add('active');
            }

            // Add click event to jump to specific slide
            indicator.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                // Enhanced debug logging
                const isFeatured = carouselElement.classList.contains('featured-news-carousel');
                console.log(`Indicator ${i+1} clicked on ${isFeatured ? 'featured' : 'regular'} carousel`);

                // Show the selected slide
                showSlide(carouselElement, i);

                // Reset the timer to prevent immediate auto-rotation
                resetTimer(carouselElement);
            });

            indicatorsContainer.appendChild(indicator);
        }
    }

    // Add navigation buttons for prev/next
    addNavigationButtons(carouselElement);

    // Set the first slide as active
    if (slides.length > 0) {
        slides[0].classList.add('active');
    }

    // Store the current slide index and create a timer
    carouselElement.dataset.currentSlide = 0;

    // Start the automatic rotation
    startRotation(carouselElement);
}

/**
 * Add static navigation buttons to a carousel
 * @param {HTMLElement} carouselElement - The carousel container element
 */
function addNavigationButtons(carouselElement) {
    // Check if buttons already exist
    if (carouselElement.querySelector('.carousel-control-prev') ||
        carouselElement.querySelector('.carousel-control-next')) {
        return;
    }

    // Skip adding navigation buttons for the featured news carousel in index.html
    if (carouselElement.classList.contains('featured-news-carousel')) {
        // Check if we're in index.html by looking for specific elements
        const newsSection = document.getElementById('news_achievements');
        if (newsSection) {
            // We're in index.html, so don't add navigation buttons to featured news
            console.log("Skipping navigation buttons for featured news in index.html");
            return;
        }
    }

    // Create previous button (static, no animations)
    const prevButton = document.createElement('button');
    prevButton.className = 'carousel-control-prev';
    prevButton.setAttribute('aria-label', 'Previous');
    prevButton.setAttribute('title', 'السابق');
    prevButton.setAttribute('type', 'button'); // Explicitly set type to button
    prevButton.style.touchAction = 'manipulation'; // Improve touch behavior

    const prevIcon = document.createElement('span');
    prevIcon.className = 'carousel-control-prev-icon';
    prevButton.appendChild(prevIcon);

    // Create next button (static, no animations)
    const nextButton = document.createElement('button');
    nextButton.className = 'carousel-control-next';
    nextButton.setAttribute('aria-label', 'Next');
    nextButton.setAttribute('title', 'التالي');
    nextButton.setAttribute('type', 'button'); // Explicitly set type to button
    nextButton.style.touchAction = 'manipulation'; // Improve touch behavior

    const nextIcon = document.createElement('span');
    nextIcon.className = 'carousel-control-next-icon';
    nextButton.appendChild(nextIcon);

    // Add event listeners with immediate effect (no animations)
    // Use mousedown instead of click to prevent any movement
    prevButton.addEventListener('mousedown', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Get current slide index
        const currentIndex = parseInt(carouselElement.dataset.currentSlide || 0);
        const slides = carouselElement.querySelectorAll('.carousel-slide');

        // Calculate previous slide index
        const prevIndex = (currentIndex - 1 + slides.length) % slides.length;

        // Show the previous slide immediately
        showSlide(carouselElement, prevIndex);

        // Reset the timer
        resetTimer(carouselElement);

        // Prevent button from getting focus which can cause movement
        return false;
    });

    // Also handle regular click for better compatibility
    prevButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
    });

    // Add touchstart event for mobile devices
    prevButton.addEventListener('touchstart', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Get current slide index
        const currentIndex = parseInt(carouselElement.dataset.currentSlide || 0);
        const slides = carouselElement.querySelectorAll('.carousel-slide');

        // Calculate previous slide index
        const prevIndex = (currentIndex - 1 + slides.length) % slides.length;

        // Show the previous slide immediately
        showSlide(carouselElement, prevIndex);

        // Reset the timer
        resetTimer(carouselElement);
    });

    // Use mousedown instead of click to prevent any movement
    nextButton.addEventListener('mousedown', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Get current slide index
        const currentIndex = parseInt(carouselElement.dataset.currentSlide || 0);
        const slides = carouselElement.querySelectorAll('.carousel-slide');

        // Calculate next slide index
        const nextIndex = (currentIndex + 1) % slides.length;

        // Show the next slide immediately
        showSlide(carouselElement, nextIndex);

        // Reset the timer
        resetTimer(carouselElement);

        // Prevent button from getting focus which can cause movement
        return false;
    });

    // Also handle regular click for better compatibility
    nextButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
    });

    // Add touchstart event for mobile devices
    nextButton.addEventListener('touchstart', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Get current slide index
        const currentIndex = parseInt(carouselElement.dataset.currentSlide || 0);
        const slides = carouselElement.querySelectorAll('.carousel-slide');

        // Calculate next slide index
        const nextIndex = (currentIndex + 1) % slides.length;

        // Show the next slide immediately
        showSlide(carouselElement, nextIndex);

        // Reset the timer
        resetTimer(carouselElement);
    });

    // Append static buttons to carousel
    carouselElement.appendChild(prevButton);
    carouselElement.appendChild(nextButton);
}

/**
 * Start the automatic rotation for a carousel
 * @param {HTMLElement} carouselElement - The carousel container element
 */
function startRotation(carouselElement) {
    // Clear any existing timer
    if (carouselElement.dataset.timerId) {
        clearInterval(parseInt(carouselElement.dataset.timerId));
    }

    // Determine which page we're on to set the appropriate rotation interval
    let rotationInterval = 15000; // Default: 15 seconds for index.html

    // Check if we're in news_list.html or news_detail.html
    const isNewsListPage = document.getElementById('newsListCarousel') !== null;
    const isNewsDetailPage = document.querySelector('.news-detail-carousel') !== null;

    if (isNewsListPage || isNewsDetailPage) {
        rotationInterval = 120000; // 2 minutes (120 seconds) for news_list.html and news_detail.html
        console.log("Setting 2-minute rotation interval for news list/detail page");
    } else {
        console.log("Setting 15-second rotation interval for index page");
    }

    // Set a new timer with the appropriate interval
    const timerId = setInterval(() => {
        rotateToNextSlide(carouselElement);
    }, rotationInterval);

    // Store the timer ID
    carouselElement.dataset.timerId = timerId;
}

/**
 * Reset the rotation timer for a carousel
 * @param {HTMLElement} carouselElement - The carousel container element
 */
function resetTimer(carouselElement) {
    // Clear the existing timer and start a new one
    if (carouselElement.dataset.timerId) {
        clearInterval(parseInt(carouselElement.dataset.timerId));
    }
    startRotation(carouselElement);
}

/**
 * Rotate to the next slide in a carousel
 * @param {HTMLElement} carouselElement - The carousel container element
 */
function rotateToNextSlide(carouselElement) {
    const slides = carouselElement.querySelectorAll('.carousel-slide');
    if (slides.length <= 1) return;

    // Get the current slide index
    let currentIndex = parseInt(carouselElement.dataset.currentSlide || 0);

    // Calculate the next slide index
    const nextIndex = (currentIndex + 1) % slides.length;

    // Show the next slide
    showSlide(carouselElement, nextIndex);
}

/**
 * Show a specific slide in a carousel with propagation animation
 * @param {HTMLElement} carouselElement - The carousel container element
 * @param {number} index - The index of the slide to show
 */
function showSlide(carouselElement, index) {
    const slides = carouselElement.querySelectorAll('.carousel-slide');
    const indicators = carouselElement.querySelectorAll('.carousel-indicator');

    if (slides.length <= 1) return; // No need to show slides if there's only one

    // Get current active slide
    const currentActiveIndex = parseInt(carouselElement.dataset.currentSlide || 0);

    // Check if this is a featured carousel for logging
    const isFeatured = carouselElement.classList.contains('featured-news-carousel');
    if (isFeatured) {
        console.log(`Showing slide ${index+1} of ${slides.length} in featured carousel`);
    }

    // Hide all slides and deactivate all indicators
    slides.forEach((slide, i) => {
        if (i !== index) {
            // Remove active class and reset animation
            slide.classList.remove('active');
            slide.style.animation = 'none';
            slide.style.opacity = 0;
            slide.style.zIndex = 1;
        }
    });

    indicators.forEach(indicator => {
        indicator.classList.remove('active');
    });

    // Show the selected slide with propagation animation
    if (slides[index]) {
        // Set z-index higher for the new active slide
        slides[index].style.zIndex = 2;

        // Reset any existing animation
        slides[index].style.animation = 'none';

        // Force reflow to ensure animation restarts
        void slides[index].offsetWidth;

        // Add active class to trigger the animation
        slides[index].classList.add('active');

        // Apply the propagation animation
        slides[index].style.animation = 'propagation-wave 0.8s ease-in-out';

        // Ensure opacity is set to 1
        slides[index].style.opacity = 1;
    }

    // Activate the corresponding indicator
    if (indicators[index]) {
        indicators[index].classList.add('active');
    }

    // Update the current slide index
    carouselElement.dataset.currentSlide = index;

    // Update the counter display if it exists (only for non-featured carousels)
    const counterDisplay = carouselElement.querySelector('.carousel-counter');
    if (counterDisplay) {
        counterDisplay.textContent = `${index + 1}/${slides.length}`;
    }
}
