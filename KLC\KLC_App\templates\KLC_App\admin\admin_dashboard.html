{% extends 'KLC_App/admin/admin_base.html' %}

{% block title %}لوحة تحكم الإدارة{% endblock %}

{% block body_class %}admin-dashboard{% endblock %}

{% block extra_css %}
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- Flatpickr Theme  -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">
    <!-- DataTables Buttons CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
    <!-- DataTables Responsive CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css">

    <style>
        /* Dashboard Header Styles */
        .dashboard-header {
            padding-bottom: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .dashboard-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            color: #64748b;
            margin-bottom: 0;
        }

        .dashboard-subtitle .fw-bold.text-primary {
            color: #dc3545 !important; /* Red color as per user preference */
            font-size: 1.1rem;
        }

        .dashboard-date {
            font-size: 0.95rem;
            color: #64748b;
            background-color: rgba(241, 245, 249, 0.8);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        /* Main Stats Cards */
        .stat-card {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .stat-card-body {
            padding: 1.5rem;
            display: flex;
            align-items: center;
            flex: 1;
        }

        .stat-icon-container {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1.25rem;
        }

        .stat-icon {
            font-size: 1.75rem;
        }

        .stat-content {
            flex: 1;
        }

        .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            line-height: 1.2;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #64748b;
            margin-bottom: 0;
        }

        .stat-footer {
            padding: 0.75rem 1.5rem;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            background-color: rgba(241, 245, 249, 0.5);
        }

        .stat-link {
            color: inherit;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            display: block;
            transition: color 0.2s;
        }

        /* Card Color Variants */
        .citizens-card .stat-icon-container {
            background-color: rgba(59, 130, 246, 0.1);
        }

        .citizens-card .stat-icon {
            color: #3b82f6;
        }

        .citizens-card .stat-link {
            color: #3b82f6;
        }

        .debt-card .stat-icon-container {
            background-color: rgba(239, 68, 68, 0.1);
        }

        .debt-card .stat-icon {
            color: #ef4444;
        }

        .debt-card .stat-link {
            color: #ef4444;
        }

        .reservations-card .stat-icon-container {
            background-color: rgba(34, 197, 94, 0.1);
        }

        .reservations-card .stat-icon {
            color: #22c55e;
        }

        .reservations-card .stat-link {
            color: #22c55e;
        }

        .transactions-card .stat-icon-container {
            background-color: rgba(14, 165, 233, 0.1);
        }

        .transactions-card .stat-icon {
            color: #0ea5e9;
        }

        .transactions-card .stat-link {
            color: #0ea5e9;
        }

        /* Section Title */
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* Section Overview Cards */
        .section-overview-card {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            height: 100%;
        }

        .section-overview-header {
            padding: 1.5rem;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .section-icon-container {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
        }

        .section-icon {
            font-size: 1.5rem;
        }

        .section-header-content {
            flex: 1;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            line-height: 1.2;
        }

        .section-subtitle {
            font-size: 0.85rem;
            color: #64748b;
            margin-bottom: 0;
        }

        .section-overview-body {
            padding: 1.5rem;
        }

        .overview-stat {
            text-align: center;
            padding: 1rem;
            background-color: rgba(241, 245, 249, 0.5);
            border-radius: 8px;
            height: 100%;
        }

        .overview-stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            line-height: 1.2;
        }

        .overview-stat-label {
            font-size: 0.85rem;
            color: #64748b;
            margin-bottom: 0;
        }

        .section-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        /* Background Color Variants */
        .bg-primary-soft {
            background-color: rgba(59, 130, 246, 0.1);
        }

        .bg-success-soft {
            background-color: rgba(34, 197, 94, 0.1);
        }

        .bg-info-soft {
            background-color: rgba(14, 165, 233, 0.1);
        }

        .bg-warning-soft {
            background-color: rgba(245, 158, 11, 0.1);
        }

        /* Latest Suggestions */
        .latest-suggestions {
            background-color: rgba(241, 245, 249, 0.5);
            border-radius: 8px;
            padding: 1rem;
            min-height: 100px;
            max-height: 250px;
            overflow-y: auto;
        }

        .suggestion-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            min-height: 80px;
            color: #94a3b8;
            text-align: center;
            gap: 0.5rem;
        }

        .suggestion-placeholder i {
            font-size: 1.5rem;
            opacity: 0.5;
        }

        .suggestion-item {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding-bottom: 0.75rem;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-header {
            font-size: 0.9rem;
        }

        .suggestion-message {
            font-size: 0.85rem;
            background-color: #f8f9fa;
            border-right: 3px solid #f59e0b;
            padding: 0.5rem !important;
        }

        .suggestion-date {
            font-size: 0.75rem;
        }

        /* Activity Cards */
        .activity-card {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .activity-card-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .activity-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0;
        }

        .activity-card-body {
            padding: 0;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .activity-list {
            list-style: none;
            margin: 0;
            padding: 0;
            flex: 1;
        }

        .activity-item {
            display: flex;
            align-items: flex-start;
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            flex-shrink: 0;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .activity-subtitle {
            font-size: 0.85rem;
            color: #64748b;
            margin-bottom: 0.5rem;
        }

        .activity-meta {
            font-size: 0.8rem;
            color: #94a3b8;
        }

        .activity-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            margin-top: auto;
        }

        .activity-empty {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            text-align: center;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #94a3b8;
        }

        .empty-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            opacity: 0.5;
        }

        /* Dark Mode Styles */
        [data-theme="dark"] .dashboard-title {
            color: #f1f5f9;
        }

        [data-theme="dark"] .dashboard-subtitle,
        [data-theme="dark"] .dashboard-date {
            color: #cbd5e1;
        }

        [data-theme="dark"] .dashboard-subtitle .fw-bold.text-primary {
            color: #ff6b6b !important; /* Brighter red for dark mode */
        }

        [data-theme="dark"] .dashboard-date {
            background-color: rgba(30, 41, 59, 0.5);
        }

        [data-theme="dark"] .section-title {
            color: #f1f5f9;
            border-bottom-color: rgba(255, 255, 255, 0.05);
        }

        [data-theme="dark"] .stat-card,
        [data-theme="dark"] .section-overview-card,
        [data-theme="dark"] .activity-card {
            background: #1e293b;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        [data-theme="dark"] .stat-label,
        [data-theme="dark"] .section-subtitle,
        [data-theme="dark"] .overview-stat-label,
        [data-theme="dark"] .activity-subtitle {
            color: #94a3b8;
        }

        [data-theme="dark"] .stat-footer,
        [data-theme="dark"] .section-overview-header,
        [data-theme="dark"] .activity-card-header,
        [data-theme="dark"] .activity-item,
        [data-theme="dark"] .activity-footer {
            border-color: rgba(255, 255, 255, 0.05);
        }

        [data-theme="dark"] .stat-footer,
        [data-theme="dark"] .overview-stat,
        [data-theme="dark"] .latest-suggestions {
            background-color: rgba(30, 41, 59, 0.5);
        }

        [data-theme="dark"] .activity-meta {
            color: #64748b;
        }

        [data-theme="dark"] .suggestion-placeholder,
        [data-theme="dark"] .empty-state {
            color: #64748b;
        }

        [data-theme="dark"] .suggestion-message {
            background-color: #1e293b;
            border-right-color: #f59e0b;
        }

        [data-theme="dark"] .suggestion-date {
            color: #94a3b8 !important;
        }

        [data-theme="dark"] .suggestion-item {
            border-bottom-color: rgba(255, 255, 255, 0.05);
        }
    </style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="dashboard-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <h1 class="dashboard-title">
            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم الرئيسية
        </h1>
        <div class="dashboard-date">
            <i class="far fa-calendar-alt me-1"></i> <span id="currentDate"></span>
        </div>
    </div>
    <p class="dashboard-subtitle text-muted">
        {% if admin_username %}
        مرحباً بك <span class="fw-bold text-primary">{{ admin_username }}</span> في لوحة التحكم، يمكنك متابعة جميع الإحصائيات والنشاطات من هنا
        {% else %}
        مرحباً بك في لوحة التحكم، يمكنك متابعة جميع الإحصائيات والنشاطات من هنا
        {% endif %}
    </p>
</div>

<!-- Main Stats Cards -->
<div class="row g-4 mb-5">
    <!-- إجمالي المواطنين -->
    <div class="col-md-6 col-xl-3">
        <div class="stat-card citizens-card">
            <div class="stat-card-body">
                <div class="stat-icon-container">
                    <i class="fas fa-users stat-icon"></i>
                </div>
                <div class="stat-content">
                    <h3 class="stat-value">{{ users_count }}</h3>
                    <p class="stat-label">إجمالي المواطنين</p>
                </div>
            </div>
            <div class="stat-footer">
                <a href="{% url 'admin_users' %}" class="stat-link">
                    إدارة المستخدمين <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- إجمالي الديون -->
    <div class="col-md-6 col-xl-3">
        <div class="stat-card debt-card">
            <div class="stat-card-body">
                <div class="stat-icon-container">
                    <i class="fas fa-money-bill-wave stat-icon"></i>
                </div>
                <div class="stat-content">
                    <h3 class="stat-value">{{ total_debt }}</h3>
                    <p class="stat-label">إجمالي الديون</p>
                </div>
            </div>
            <div class="stat-footer">
                <a href="{% url 'admin_users' %}" class="stat-link">
                    تفاصيل الديون <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- حجوزات القاعة -->
    <div class="col-md-6 col-xl-3">
        <div class="stat-card reservations-card">
            <div class="stat-card-body">
                <div class="stat-icon-container">
                    <i class="fas fa-calendar-alt stat-icon"></i>
                </div>
                <div class="stat-content">
                    <h3 class="stat-value">{{ hall_reservations_count }}</h3>
                    <p class="stat-label">حجوزات القاعة</p>
                </div>
            </div>
            <div class="stat-footer">
                <a href="{% url 'admin_reservations' %}" class="stat-link">
                    إدارة الحجوزات <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- الطلبات -->
    <div class="col-md-6 col-xl-3">
        <div class="stat-card transactions-card">
            <div class="stat-card-body">
                <div class="stat-icon-container">
                    <i class="fas fa-tasks stat-icon"></i>
                </div>
                <div class="stat-content">
                    <h3 class="stat-value">{{ transactions_count }}</h3>
                    <p class="stat-label">الطلبات</p>
                </div>
            </div>
            <div class="stat-footer">
                <a href="{% url 'admin_transactions' %}" class="stat-link">
                    إدارة الطلبات <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Section Overviews -->
<div class="section-title mb-4">
    <h2><i class="fas fa-th-large me-2"></i> نظرة عامة على الأقسام</h2>
</div>

<div class="row g-4 mb-5">
    <!-- Users Section Overview -->
    <div class="col-md-6">
        <div class="section-overview-card">
            <div class="section-overview-header">
                <div class="section-icon-container bg-primary-soft">
                    <i class="fas fa-users-cog section-icon text-primary"></i>
                </div>
                <div class="section-header-content">
                    <h3 class="section-title">إدارة المستخدمين</h3>
                    <p class="section-subtitle">إدارة بيانات المواطنين والديون</p>
                </div>
            </div>
            <div class="section-overview-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="overview-stat">
                            <div class="overview-stat-value">{{ users_count }}</div>
                            <div class="overview-stat-label">إجمالي المستخدمين</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="overview-stat">
                            <div class="overview-stat-value">{{ total_debt }}</div>
                            <div class="overview-stat-label">إجمالي الديون</div>
                        </div>
                    </div>
                </div>
                <div class="section-actions mt-3">
                    <a href="{% url 'admin_users' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-cog me-1"></i> إدارة المستخدمين
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservations Section Overview -->
    <div class="col-md-6">
        <div class="section-overview-card">
            <div class="section-overview-header">
                <div class="section-icon-container bg-success-soft">
                    <i class="fas fa-calendar-alt section-icon text-success"></i>
                </div>
                <div class="section-header-content">
                    <h3 class="section-title">حجوزات القاعة</h3>
                    <p class="section-subtitle">إدارة حجوزات قاعة المناسبات</p>
                </div>
            </div>
            <div class="section-overview-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="overview-stat">
                            <div class="overview-stat-value">{{ hall_reservations_count }}</div>
                            <div class="overview-stat-label">إجمالي الحجوزات</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="overview-stat">
                            <div class="overview-stat-value">
                                {% if hall_reservations %}
                                    {{ hall_reservations.0.event_type|default:"--" }}
                                {% else %}
                                    --
                                {% endif %}
                            </div>
                            <div class="overview-stat-label">آخر نوع حجز</div>
                        </div>
                    </div>
                </div>
                <div class="section-actions mt-3">
                    <a href="{% url 'admin_reservations' %}" class="btn btn-success btn-sm">
                        <i class="fas fa-calendar-check me-1"></i> إدارة الحجوزات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions Section Overview -->
    <div class="col-md-6">
        <div class="section-overview-card">
            <div class="section-overview-header">
                <div class="section-icon-container bg-info-soft">
                    <i class="fas fa-tasks section-icon text-info"></i>
                </div>
                <div class="section-header-content">
                    <h3 class="section-title">إدارة الطلبات</h3>
                    <p class="section-subtitle">متابعة وإدارة طلبات المواطنين</p>
                </div>
            </div>
            <div class="section-overview-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="overview-stat">
                            <div class="overview-stat-value">{{ transactions_count }}</div>
                            <div class="overview-stat-label">إجمالي الطلبات</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="overview-stat">
                            <div class="overview-stat-value">
                                <a href="{% url 'transaction_statistics' %}" class="text-decoration-none">
                                    <i class="fas fa-chart-bar"></i>
                                </a>
                            </div>
                            <div class="overview-stat-label">إحصائيات الطلبات</div>
                        </div>
                    </div>
                </div>
                <div class="section-actions mt-3">
                    <a href="{% url 'admin_transactions' %}" class="btn btn-info btn-sm text-white">
                        <i class="fas fa-list-check me-1"></i> إدارة الطلبات
                    </a>
                    <a href="{% url 'transaction_statistics' %}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-chart-line me-1"></i> الإحصائيات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Suggestions Section Overview -->
    <div class="col-md-6">
        <div class="section-overview-card">
            <div class="section-overview-header">
                <div class="section-icon-container bg-warning-soft">
                    <i class="fas fa-comment-alt section-icon text-warning"></i>
                </div>
                <div class="section-header-content">
                    <h3 class="section-title">الشكاوي والاقتراحات</h3>
                    <p class="section-subtitle">متابعة شكاوي واقتراحات المواطنين</p>
                </div>
            </div>
            <div class="section-overview-body">
                <div class="row g-3">
                    <div class="col-12">
                        <div class="overview-stat">
                            <div class="overview-stat-label mb-2">آخر الشكاوي والاقتراحات</div>
                            <div class="latest-suggestions">
                                {% if suggestions %}
                                    {% for suggestion in suggestions %}
                                    <div class="suggestion-item mb-2">
                                        <div class="suggestion-header d-flex justify-content-between align-items-center mb-1">
                                            <span class="suggestion-name fw-bold">{{ suggestion.full_name }}</span>
                                            <small class="suggestion-date text-muted">{{ suggestion.created_at }}</small>
                                        </div>
                                        <div class="suggestion-message p-2 bg-light rounded">
                                            {{ suggestion.message|truncatechars:100 }}
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="suggestion-placeholder">
                                        <i class="fas fa-comments text-muted"></i>
                                        <span>لا توجد شكاوي أو اقتراحات حالياً</span>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="section-actions mt-3">
                    <a href="{% url 'admin_suggestions' %}" class="btn btn-warning btn-sm">
                        <i class="fas fa-comment-dots me-1"></i> عرض الشكاوي والاقتراحات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="section-title mb-4">
    <h2><i class="fas fa-history me-2"></i> آخر النشاطات</h2>
</div>

<div class="row g-4 mb-4">
    <!-- Recent Transactions -->
    <div class="col-md-6">
        <div class="activity-card">
            <div class="activity-card-header">
                <h3 class="activity-title">
                    <i class="fas fa-tasks me-2 text-info"></i>
                    آخر الطلبات
                </h3>
            </div>
            <div class="activity-card-body">
                <ul class="activity-list">
                    {% for transaction in transactions|slice:":5" %}
                    <li class="activity-item">
                        <div class="activity-icon bg-info-soft">
                            <i class="fas fa-file-alt text-info"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">{{ transaction.full_name }}</div>
                            <div class="activity-subtitle">{{ transaction.transaction_type }}</div>
                            <div class="activity-meta">
                                <span class="activity-time">
                                    <i class="far fa-clock me-1"></i> {{ transaction.created_at }}
                                </span>
                            </div>
                        </div>
                    </li>
                    {% empty %}
                    <li class="activity-empty">
                        <div class="empty-state">
                            <i class="fas fa-inbox empty-icon"></i>
                            <p>لا يوجد طلبات حديثة</p>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
                <div class="activity-footer">
                    <a href="{% url 'admin_transactions' %}" class="btn btn-outline-info btn-sm w-100">
                        عرض كل الطلبات <i class="fas fa-arrow-left ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reservations -->
    <div class="col-md-6">
        <div class="activity-card">
            <div class="activity-card-header">
                <h3 class="activity-title">
                    <i class="fas fa-calendar-alt me-2 text-success"></i>
                    آخر الحجوزات
                </h3>
            </div>
            <div class="activity-card-body">
                <ul class="activity-list">
                    {% for reservation in hall_reservations|slice:":5" %}
                    <li class="activity-item">
                        <div class="activity-icon bg-success-soft">
                            <i class="fas fa-calendar-check text-success"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">{{ reservation.full_name }}</div>
                            <div class="activity-subtitle">{{ reservation.event_type }}</div>
                            <div class="activity-meta">
                                <span class="activity-time">
                                    <i class="far fa-clock me-1"></i> {{ reservation.created_at }}
                                </span>
                            </div>
                        </div>
                    </li>
                    {% empty %}
                    <li class="activity-empty">
                        <div class="empty-state">
                            <i class="fas fa-inbox empty-icon"></i>
                            <p>لا يوجد حجوزات حديثة</p>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
                <div class="activity-footer">
                    <a href="{% url 'admin_reservations' %}" class="btn btn-outline-success btn-sm w-100">
                        عرض كل الحجوزات <i class="fas fa-arrow-left ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Set the active page for the sidebar and initialize dashboard components
    document.addEventListener('DOMContentLoaded', function() {
        // Add active class to dashboard link
        const dashboardLink = document.querySelector('a[href="{% url "admin_dashboard" %}"]');
        if (dashboardLink) {
            dashboardLink.classList.add('active');
        }

        // Display current date in Arabic format
        const currentDateElement = document.getElementById('currentDate');
        if (currentDateElement) {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            currentDateElement.textContent = now.toLocaleDateString('ar-EG', options);
        }

        // Add hover effects to section cards
        const sectionCards = document.querySelectorAll('.section-overview-card');
        sectionCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.1)';
                this.style.transition = 'all 0.3s ease';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.05)';
            });
        });

        // Add hover effects to activity cards
        const activityCards = document.querySelectorAll('.activity-card');
        activityCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.1)';
                this.style.transition = 'all 0.3s ease';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.05)';
            });
        });
    });
</script>
{% endblock %}
