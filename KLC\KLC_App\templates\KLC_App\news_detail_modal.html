<!-- Professional Modal for displaying full news content -->
<div
  class="modal fade"
  id="newsDetailModal"
  tabindex="-1"
  aria-labelledby="newsDetailModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
        <h5 class="modal-title" id="newsDetailModalLabel"><!-- Title will be inserted here --></h5>
      </div>
      <div class="modal-body">
        <div class="news-modal-content">
          <!-- Professional News Gallery Container -->
          <div class="news-gallery-container mb-4">
            <!-- Main Gallery with Enhanced Styling -->
            <div class="news-gallery">
              <!-- Single image view (for backward compatibility) -->
              <div class="news-gallery-slide active" id="singleImageSlide">
                <img src="" alt="" id="newsDetailImage">
              </div>
              <!-- Additional image slides will be added dynamically -->
            </div>

            <!-- Enhanced Gallery Navigation -->
            <div class="gallery-prev">
              <i class="fas fa-chevron-left"></i>
            </div>
            <div class="gallery-next">
              <i class="fas fa-chevron-right"></i>
            </div>

            <!-- Gallery Dots with Professional Styling -->
            <div class="news-gallery-nav" id="galleryDots">
              <!-- Dots will be added dynamically -->
            </div>

            <!-- Image Counter -->
            <div class="gallery-counter">
              <span id="currentImageIndex">1</span>/<span id="totalImagesCount">1</span>
            </div>

            <!-- Fullscreen Button -->
            <div class="gallery-fullscreen">
              <i class="fas fa-expand"></i>
            </div>
          </div>

          <!-- Professional Gallery Thumbnails -->
          <div class="gallery-thumbnails mb-4" id="galleryThumbnails">
            <!-- Thumbnails will be added dynamically -->
          </div>

          <!-- Enhanced News Metadata -->
          <div class="news-modal-meta">
            <div class="news-modal-date">
              <i class="fas fa-calendar-alt me-1"></i>
              <span class="news-date-title">تاريخ النشر: </span>
              <span class="news-date" id="newsDetailDate"><!-- Date will be inserted here --></span>
            </div>
            <div class="news-modal-category">
              <i class="fas fa-tag me-1"></i>
              <span id="newsDetailCategory"><!-- Category will be inserted here --></span>
            </div>
          </div>

          <!-- Enhanced News Description -->
          <div class="news-modal-description">
            <p id="newsDetailDescription"><!-- Description will be inserted here --></p>
          </div>

          <!-- Professional Video Container -->
          <div class="news-modal-video" id="newsDetailVideoContainer">
            <!-- Video will be inserted here if available -->
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" id="showVideoBtn" data-last-video="">
          <i class="fas fa-play-circle me-1"></i> عرض الفيديو
        </button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="fas fa-times me-1"></i> إغلاق
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Add CSS for image counter and fullscreen button -->
<style>
  .gallery-counter {
    position: absolute;
    top: 15px;
    left: 15px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9rem;
    z-index: 10;
    font-weight: 600;
  }

  .gallery-fullscreen {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 35px;
    height: 35px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
  }

  .gallery-fullscreen:hover {
    background-color: rgba(220, 53, 69, 0.8);
    transform: scale(1.1);
  }

  /* Fullscreen mode styles */
  .news-gallery.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background-color: rgba(0, 0, 0, 0.95);
  }

  .news-gallery.fullscreen .news-gallery-slide img {
    max-height: 90vh;
  }

  .news-gallery.fullscreen + .gallery-prev,
  .news-gallery.fullscreen + .gallery-next {
    z-index: 10000;
  }

  .news-gallery.fullscreen ~ .gallery-fullscreen {
    background-color: rgba(220, 53, 69, 0.8);
  }

  .news-gallery.fullscreen ~ .gallery-fullscreen i:before {
    content: "\f78c"; /* fa-compress icon */
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .gallery-counter {
      top: 10px;
      left: 10px;
      font-size: 0.8rem;
      padding: 4px 8px;
    }

    .gallery-fullscreen {
      top: 10px;
      right: 10px;
      width: 30px;
      height: 30px;
    }
  }
</style>
