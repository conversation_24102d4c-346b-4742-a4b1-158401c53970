/* Common section styling for consistent right alignment */
section {
    padding: 3rem 0;
    text-align: center;
    background-color: #f8f9fa;
}

section:nth-child(odd) {
    background-color: #fff;
}

/* Section title styling to match the image */
section h2 {
    text-align: center;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
}

section h2 i {
    margin-left: 0.5rem;
    color: #2c3e50;
    font-size: 1.5rem;
}

/* Subtitle styling */
section p.lead {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #7f8c8d;
    font-size: 1.1rem;
}

/* Add a subtle separator after the lead paragraph */
section p.lead::after {
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    background: rgba(0, 0, 0, 0.05);
    margin-top: 1.5rem;
}

/* Section header with line */
.section-header {
    position: relative;
    margin-bottom: 3rem;
}

.section-header h2 {
    position: relative;
    z-index: 2;
    padding: 0 1.5rem;
}

.section-header-line {
    height: 4px;
    width: 80px;
    background: linear-gradient(90deg, #dc2626 0%, #ef4444 100%);
    margin: 0.5rem auto 0;
    border-radius: 2px;
}

/* Modern About Section Styling */
.about-section {
    position: relative;
    overflow: hidden;
    padding: 4rem 0;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.01) 25%, transparent 25%, transparent 75%, rgba(0,0,0,0.01) 75%),
                linear-gradient(45deg, rgba(0,0,0,0.01) 25%, transparent 25%, transparent 75%, rgba(0,0,0,0.01) 75%);
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
    opacity: 0.3;
    z-index: 0;
}

/* About intro styling */
.about-intro-container {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.about-intro {
    font-size: 1.25rem;
    line-height: 1.8;
    color: #2c3e50;
    font-weight: 500;
    position: relative;
    padding: 0 20px;
}

.about-intro::after {
    content: '';
    position: absolute;
    bottom: -15px;
    right: 50%;
    transform: translateX(50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #dc2626 0%, #ef4444 100%);
    border-radius: 2px;
}

/* About overview styling */
.about-overview {
    text-align: center;
    position: relative;
    padding-bottom: 20px;
}

.about-overview::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 1px;
    background-color: rgba(220, 38, 38, 0.2);
}

.about-overview-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 20px;
    box-shadow: 0 10px 20px rgba(220,38,38,0.2);
}

.about-overview-icon i {
    font-size: 2.5rem;
    color: white;
}

.about-overview-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

/* Features grid styling */
.about-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.about-feature-item {
    background-color: white;
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: flex-start;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    text-align: right;
}

.about-feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, #dc2626, #ef4444);
    opacity: 0.8;
}

.about-feature-icon {
    width: 60px;
    height: 60px;
    min-width: 60px;
    background-color: rgba(220, 38, 38, 0.1);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 20px;
}

.about-feature-icon i {
    font-size: 1.5rem;
    color: #dc2626;
}

.about-feature-content {
    flex: 1;
}

.about-feature-content h4 {
    margin: 0 0 10px 0;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.2rem;
}

.about-feature-content p {
    margin: 0;
    color: #64748b;
    font-size: 1rem;
    line-height: 1.6;
}

/* Dark theme support */
[data-theme="dark"] section {
    background-color: #1a1a1a;
}

[data-theme="dark"] section:nth-child(odd) {
    background-color: #2c3e50;
}

[data-theme="dark"] section h2 {
    color: #ecf0f1;
}

[data-theme="dark"] section h2 i {
    color: #3498db;
}

[data-theme="dark"] section p.lead {
    color: #bdc3c7;
}

[data-theme="dark"] section p.lead::after {
    background: rgba(255, 255, 255, 0.05);
}

/* Dark theme for about section */
[data-theme="dark"] .about-intro {
    color: #ecf0f1;
}

[data-theme="dark"] .about-overview-title {
    color: #ecf0f1;
}

[data-theme="dark"] .about-overview-icon {
    background: linear-gradient(135deg, #991b1b 0%, #b91c1c 100%);
}

[data-theme="dark"] .about-overview::after {
    background-color: rgba(236, 240, 241, 0.1);
}

[data-theme="dark"] .about-feature-item {
    background-color: #2c3e50;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .about-feature-item::before {
    background: linear-gradient(to bottom, #991b1b, #b91c1c);
}

[data-theme="dark"] .about-feature-icon {
    background-color: rgba(220, 38, 38, 0.2);
}

[data-theme="dark"] .about-feature-content h4 {
    color: #ecf0f1;
}

[data-theme="dark"] .about-feature-content p {
    color: #bdc3c7;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .about-section {
        padding: 3rem 0;
    }

    .about-features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .about-overview-icon {
        width: 90px;
        height: 90px;
    }

    .about-overview-icon i {
        font-size: 2.2rem;
    }

    .about-feature-icon {
        width: 50px;
        height: 50px;
        min-width: 50px;
    }

    .about-feature-icon i {
        font-size: 1.3rem;
    }
}

@media (max-width: 768px) {
    section {
        padding: 2rem 0;
    }

    .about-section {
        padding: 2.5rem 0;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    .about-intro {
        font-size: 1.1rem;
        padding: 0 15px;
    }

    .about-features-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        margin-top: 30px;
    }

    .about-feature-item {
        padding: 20px;
    }

    .about-feature-content h4 {
        font-size: 1.1rem;
    }

    .about-feature-content p {
        font-size: 0.95rem;
    }

    .about-overview-title {
        font-size: 1.3rem;
    }

    /* Dark theme mobile adjustments */
    [data-theme="dark"] .about-feature-item {
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
    }
}

/* Additional mobile adjustments for very small screens */
@media (max-width: 576px) {
    section {
        padding: 1.5rem 0;
    }

    .about-section {
        padding: 2rem 0;
    }

    .container.px-4 {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    .about-intro-container {
        margin-bottom: 2rem !important;
    }

    .about-intro {
        font-size: 1rem;
        line-height: 1.6;
        padding: 0 10px;
    }

    .about-overview {
        padding-bottom: 15px;
    }

    .about-overview-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 15px;
    }

    .about-overview-icon i {
        font-size: 1.8rem;
    }

    .about-overview-title {
        font-size: 1.2rem;
    }

    .about-features-grid {
        gap: 12px;
        margin-top: 20px;
    }

    .about-feature-item {
        padding: 15px;
        border-radius: 10px;
    }

    .about-feature-icon {
        width: 40px;
        height: 40px;
        min-width: 40px;
        margin-left: 12px;
    }

    .about-feature-icon i {
        font-size: 1.1rem;
    }

    .about-feature-content h4 {
        font-size: 1rem;
        margin-bottom: 5px;
    }

    .about-feature-content p {
        font-size: 0.85rem;
        line-height: 1.5;
    }

    .section-header h2 {
        font-size: 1.5rem;
    }

    /* Dark theme small screen adjustments */
    [data-theme="dark"] .about-feature-item {
        background-color: rgba(44, 62, 80, 0.95);
    }

    .row.gx-4 {
        margin-left: 0;
        margin-right: 0;
    }
}
